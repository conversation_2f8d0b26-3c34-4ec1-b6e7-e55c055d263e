<v-layout class="banner-settings-container" wrap>
  <v-flex md12>
    <v-layout
      class="task-bar app-theme__center-content--header no-highlight justify-space-between"
    >
      <p class="ma-0">Environment Banner Settings</p>
      <div>
        <InformationTooltip :bottom="true">
          <v-layout slot="content">
            Controls whether the environment banner is displayed in the app bar.
            When enabled, the banner will show regardless of environment.
            When disabled, the banner follows default environment-based logic.
          </v-layout>
        </InformationTooltip>
      </div>
    </v-layout>
  </v-flex>
  <v-flex md12 class="pa-2">
    <v-form ref="form">
      <v-layout row wrap>
        <v-flex md12 class="pb-3">
          <v-switch
            v-model="shouldShowBanner"
            label="Force Show Environment Banner"
            hint="When enabled, the environment banner will always be visible regardless of the current environment"
            persistent-hint
            :disabled="!isAuthorised() || isLoading"
            @change="onBannerSettingChange"
          />
        </v-flex>
        <v-flex md12>
          <v-layout justify-space-between align-center>
            <div>
              <p class="mb-1"><strong>Current Environment:</strong> {{ currentEnvironment }}</p>
              <p class="mb-1"><strong>Default Banner Logic:</strong> {{ defaultBannerLogic }}</p>
              <p class="mb-1"><strong>Effective Banner State:</strong> {{ effectiveBannerState }}</p>
            </div>
            <v-btn
              color="primary"
              @click="resetToDefault"
              :disabled="!isAuthorised() || isLoading || !hasCustomSetting"
              small
            >
              Reset to Default
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-form>
  </v-flex>
</v-layout>
