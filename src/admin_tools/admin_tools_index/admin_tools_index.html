<v-layout class="pa-2">
  <v-flex md12>
    <v-flex md12 class="pt-4">
      <v-layout class="banner-settings-container" wrap>
        <v-flex md12>
          <v-layout
            class="task-bar app-theme__center-content--header no-highlight justify-space-between"
          >
            <p class="ma-0">Environment Banner Settings</p>
            <div>
              <InformationTooltip :bottom="true">
                <v-layout slot="content">
                  Controls whether the environment banner is displayed in the app bar.
                  The banner resets to environment-based logic on page refresh but can be manually disabled.
                </v-layout>
              </InformationTooltip>
            </div>
          </v-layout>
        </v-flex>
        <v-flex md12 class="pa-2">
          <v-form ref="form">
            <v-layout row wrap>
              <v-flex md12 class="pb-3">
                <v-switch
                  v-model="bannerEnabled"
                  label="Show Environment Banner"
                  hint="Disable to hide the banner until page refresh. Banner will reset to environment logic on refresh."
                  persistent-hint
                  :disabled="!isAuthorised()"
                  @change="onBannerToggle"
                />
              </v-flex>
              <v-flex md12>
                <v-layout column>
                  <div class="mb-3">
                    <p class="mb-1"><strong>Current Environment:</strong> {{ currentEnvironment }}</p>
                  </div>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
      </v-layout>
    </v-flex>
    <CompanyNotification />
  </v-flex>
</v-layout>
