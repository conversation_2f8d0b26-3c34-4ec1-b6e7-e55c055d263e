import CompanyNotification from '@/admin_tools/components/company_notification/index.vue';
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useBannerSettingsStore } from '@/store/modules/BannerSettingsStore';
import { Component, Vue } from 'vue-property-decorator';
@Component({
  components: { CompanyNotification, InformationTooltip },
})
export default class AdminTools extends Vue {
  private bannerSettingsStore = useBannerSettingsStore();

  // Only GoDesta admin users should have the ability to action requests
  public isAuthorised(): boolean {
    return hasAdminRole();
  }

  /**
   * Gets the current environment
   */
  get currentEnvironment(): string {
    return this.bannerSettingsStore.currentEnvironment;
  }

  /**
   * Gets whether the banner is currently enabled
   */
  get bannerEnabled(): boolean {
    return this.bannerSettingsStore.shouldShowBanner;
  }

  /**
   * Handles toggling the banner on/off
   */
  public onBannerToggle(value: boolean): void {
    if (!this.isAuthorised()) {
      showNotification('You do not have permission to modify banner settings.');
      return;
    }

    this.bannerSettingsStore.toggleBanner();
    if (value) {
      showNotification('Environment banner enabled.', {
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Environment banner disable.', {
        type: HealthLevel.SUCCESS,
      });
    }
  }
}
