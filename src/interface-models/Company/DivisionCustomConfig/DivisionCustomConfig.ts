import { DivisionDefaultValues } from '@/interface-models/Company/DivisionCustomConfig/DefaultValues/DivisionDefaultValues';
import DriverAppDivisionSettings from '@/interface-models/Company/DivisionCustomConfig/DriverApp/DriverAppDivisionSettings';
import { DivisionOperationDetails } from '@/interface-models/Company/DivisionCustomConfig/Operations/DivisionOperationDetails';
import { QuotePreferences } from '@/interface-models/Company/DivisionCustomConfig/QuotePreferences';
import DivisionThemeSettings from '@/interface-models/Company/DivisionCustomConfig/Theme/DivisionThemeSettings';
import { AssetMaintenanceRecordType } from '@/interface-models/FleetAsset/AssetMaintenance/AssetMaintenanceRecordType';

import { AccountingDivisionSettings } from '@/interface-models/Company/DivisionCustomConfig/Accounting/AccountingDivisionSettings';
import { InvoiceAdjustmentDivisionSettings } from '@/interface-models/InvoiceAdjustment/InvoiceAdjustmentDivision/InvoiceAdjustmentDivisionSettings';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { SupportTicketDivisionSettings } from '@/interface-models/SupportTicket/SupportTicketDivision.ts/SupportTicketDivisionSettings';

/**
 * Contains division-specific configurations for various processes within
 * the frontend and mobile apps. These are included in the initial payloads sent
 * to frontend and mobile.
 */
export interface DivisionCustomConfig {
  /**
   * Settings related to Support Tickets, including email recipients.
   */
  supportTicket?: SupportTicketDivisionSettings;

  /**
   * Settings related to Invoice Adjustment, including email recipients and
   * approval requirements. These values MUST be defined for Invoice Adjustments
   * to be approved.
   */
  invoiceAdjustment?: InvoiceAdjustmentDivisionSettings;

  /**
   * Division-specific operations settings, such as default load duration.
   */
  operations?: DivisionOperationDetails;

  /**
   * Division-specific subscriptions.
   */
  subscriptions?: DivisionSubscriptions;

  /**
   * Settings to be sent to the mobile app to override default functionality.
   */
  driverApp?: DriverAppDivisionSettings;

  /**
   * Theming options to be applied on the frontend.
   */
  theme?: DivisionThemeSettings;

  /**
   * Compliance settings for the division.
   */
  compliance?: DivisionComplianceSettings;

  /**
   * Company-specific quote preferences.
   */
  quotePreferences?: QuotePreferences;

  /**
   * Division-specific default values.
   */
  defaultValues?: DivisionDefaultValues;

  /**
   * Division specific settings related to accounting and pricing processes.
   * These settings are used to configure how the division handles rate
   * calculations, invoice generation, and other financial operations.
   */
  accounting?: AccountingDivisionSettings;
}

interface DivisionTruckComplianceSettings {
  requiredInspectionTypes: AssetMaintenanceRecordType[];
  serviceRateAlerts: boolean;
  fuelSurchargeAlerts: boolean;
  registrationAlerts: boolean;
  insuranceAlerts: boolean;
  additionalEquipmentAlerts: boolean;
}

interface DivisionMajorCraneInspectionSettings {
  lessThanTenMt: boolean;
  tenMtPlus: boolean;
}
interface DivisionCraneInspectionSettings {
  minor: boolean;
  major: DivisionMajorCraneInspectionSettings;
}
interface DivisionCraneComplianceSettings {
  requiredInspections: DivisionCraneInspectionSettings;
}

interface DivisionComplianceSettings {
  truck: DivisionTruckComplianceSettings;
  crane: DivisionCraneComplianceSettings;
}

export interface DivisionReportSettings {
  allowedAccessMethods: ReportAccessMethodTypes[];
}
export interface DivisionCustomConfig {
  supportTicket?: SupportTicketDivisionSettings;
  invoiceAdjustment?: InvoiceAdjustmentDivisionSettings;
  operations?: DivisionOperationDetails;
  subscriptions?: DivisionSubscriptions;
  driverApp?: DriverAppDivisionSettings;
  theme?: DivisionThemeSettings;
  compliance?: DivisionComplianceSettings;
  quotePreferences?: QuotePreferences;
  reports: DivisionReportSettings;
  /**
   * Controls whether the environment banner should be shown in the app bar.
   * When true, overrides the default environment-based logic.
   * When false or undefined, uses default environment-based logic.
   */
  shouldShowBanner?: boolean;
}
