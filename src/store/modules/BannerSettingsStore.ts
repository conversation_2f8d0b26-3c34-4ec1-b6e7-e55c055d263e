import Environment from '@/configuration/environment';
import { defineStore } from 'pinia';
import { ComputedRef, Ref, computed, ref } from 'vue';

export const useBannerSettingsStore = defineStore('bannerSettingsStore', () => {
  // State: Controls whether banner is manually disabled
  const isBannerManuallyDisabled: Ref<boolean> = ref(false);

  /**
   * Gets the current environment value from the configuration
   */
  const currentEnvironment: ComputedRef<string> = computed(() => {
    return Environment.value('environment') || 'production';
  });

  /**
   * Determines if the banner should be shown based on default environment logic
   */
  const shouldShowBannerByEnvironment: ComputedRef<boolean> = computed(() => {
    const env = currentEnvironment.value;
    return env !== 'production' && env !== '$VUE_APP_ENV';
  });

  /**
   * Determines if the banner should be shown (final decision)
   * - Resets to environment-based logic on refresh
   * - Can be manually disabled via admin panel
   */
  const shouldShowBanner: ComputedRef<boolean> = computed(() => {
    // If manually disabled, don't show banner
    if (isBannerManuallyDisabled.value) {
      return false;
    }
    // Otherwise, use environment-based logic
    return shouldShowBannerByEnvironment.value;
  });

  /**
   * Gets the display name for the environment
   */
  const environmentDisplayName: ComputedRef<string> = computed(() => {
    const env = currentEnvironment.value;
    switch (env) {
      case 'local':
        return 'LOCAL';
      case 'dev':
        return 'DEV';
      case 'demo':
        return 'DEMO';
      case 'production':
        return 'PROD';
      default:
        return env.toUpperCase();
    }
  });

  /**
   * Toggles the banner state
   */
  function toggleBanner(): void {
    isBannerManuallyDisabled.value = !isBannerManuallyDisabled.value;
  }

  return {
    currentEnvironment,
    shouldShowBanner,
    environmentDisplayName,
    toggleBanner,
  };
});
