<template>
  <v-layout wrap class="fleet-asset-index">
    <v-flex
      md12
      v-if="fleetAssetDetails && (fleetAssetOwnerSummary || isNewFleetAsset)"
    >
      <v-layout wrap>
        <v-flex md12 v-if="isDialog">
          <v-layout
            justify-space-between
            class="task-bar app-theme__center-content--header no-highlight"
          >
            <span
              ><span v-if="isDialog">Fleet Asset - </span
              >{{ fleetAssetName }}</span
            >
            <div
              v-if="isDialog"
              class="app-theme__center-content--closebutton"
              @click="closeFleetAssetDialog"
            >
              <v-icon class="app-theme__center-content--closebutton--icon"
                >fal fa-times</v-icon
              >
            </div>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout
            class="app-theme__center-content--body dialog-content"
            row
            wrap
            :class="isDialog ? 'main-content-dialog' : 'main-content-route'"
          >
            <v-flex
              lg3
              md4
              class="side-column app-bgcolor--400 app-bordercolor--600 app-borderside--r-shadow"
            >
              <v-layout row wrap>
                <v-flex
                  md12
                  class="app-bgcolor--250 app-bordercolor--600 app-borderside--b"
                >
                  <v-layout row wrap py-2 px-2>
                    <v-flex md12>
                      <v-form>
                        <SelectEntity
                          :key="fleetAssetDetails.fleetAssetId"
                          :entityTypes="[
                            entityType.FLEET_ASSET_OWNER,
                            entityType.FLEET_ASSET,
                            entityType.DRIVER,
                          ]"
                          v-if="!isDialog"
                          :isRouteSelect="true"
                          :disabled="isEdited || !fleetAssetDetails._id"
                          class="mb-3"
                        />
                      </v-form>
                    </v-flex>
                    <v-flex md12 class="side-column__summaryitem">
                      <v-layout justify-space-between align-start>
                        <span class="side-column__summaryitem--key">
                          Status
                        </span>
                        <span
                          class="side-column__summaryitem--value status-container"
                          :class="currentStatus.color"
                        >
                          {{ currentStatus.statusName }}
                        </span>
                      </v-layout>
                    </v-flex>
                    <v-flex
                      md12
                      class="side-column__summaryitem"
                      v-for="infoItem in summaryInfoList"
                      :key="infoItem.id"
                    >
                      <v-layout
                        v-if="summaryInfoList"
                        justify-space-between
                        align-start
                      >
                        <span class="side-column__summaryitem--key">
                          {{ infoItem.title }}
                        </span>
                        <span
                          v-if="infoItem.id !== 'subcontractorName'"
                          class="side-column__summaryitem--value"
                        >
                          {{ infoItem.value }}
                        </span>
                        <span
                          v-if="infoItem.id === 'subcontractorName'"
                          class="side-column__summaryitem--value"
                        >
                          <b
                            v-if="!isDialog"
                            @click="viewFleetAssetOwner"
                            style="cursor: pointer; color: #2196f3"
                            >{{ infoItem.value }}</b
                          >
                          <b v-if="isDialog">{{ infoItem.value }}</b>
                        </span>
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex
                  md12
                  v-for="menuOption in menuOptions"
                  :key="menuOption.id"
                >
                  <v-layout row wrap>
                    <v-flex md12 class="app-bordercolor--600 app-borderside--b">
                      <v-layout>
                        <h5 class="subheader--bold--12 px-3 pt-3 pb-1">
                          {{ menuOption.title }}
                        </h5>
                      </v-layout>
                    </v-flex>
                    <v-flex
                      md12
                      v-for="menuItem in filteredMenuItems(menuOption.items)"
                      :key="menuItem.id"
                      class="app-bgcolor--300 app-bordercolor--600 app-borderside--b side-column__button"
                      :class="[
                        selectedViewType === menuItem.id ? 'active-state' : '',
                        menuItem.isActive
                          ? 'menu-item-selectable'
                          : 'menu-item-disabled',
                      ]"
                      @click="setSelectedView(menuItem.id)"
                    >
                      <v-layout align-center>
                        <span class="button-label"
                          ><span class="pr-2">-</span>{{ menuItem.title }}</span
                        >
                      </v-layout>
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex lg9 md8 class="dialog-content__scrollable">
              <v-layout
                class="dialog-toolbar center-section__top-toolbar"
                style="position: relative"
                align-center
              >
                <v-btn
                  depressed
                  v-if="isEdited"
                  @click="findAndSetSelectedFleetAssetDetails(isNewFleetAsset)"
                  outline
                  color="error"
                  small
                >
                  Cancel
                </v-btn>
                <v-btn
                  depressed
                  v-if="!isEdited && !isDialog && selectedViewType !== 'SERV'"
                  @click="goToSubcontractorIndexRoute"
                  outline
                  color="error"
                  small
                >
                  exit
                </v-btn>
                <v-spacer />
                <v-btn
                  :disabled="!isAuthorised()"
                  depressed
                  color="blue"
                  small
                  v-if="!isEdited && isForm"
                  @click="editFleetAssetDetails"
                >
                  Edit
                </v-btn>
                <v-btn
                  v-if="isEdited"
                  depressed
                  color="blue"
                  small
                  @click="saveFleetAssetDetails"
                  :disabled="isAwaitingFleetAssetResponse"
                >
                  Save
                </v-btn>
              </v-layout>
              <v-layout
                class="scrollable"
                v-if="fleetAssetOwnerSummary"
                :key="
                  fleetAssetDetails ? fleetAssetDetails.fleetAssetId : 'new'
                "
              >
                <v-form ref="fleetAssetForm" style="width: 100%" v-if="isForm">
                  <v-layout
                    fill-height
                    v-if="isTruckDetails(fleetAssetDetails)"
                  >
                    <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                      <FleetAssetTruckKeyInformation
                        v-if="selectedViewType === 'KEY'"
                        :isCancel="isCancel"
                        :fleetAsset="fleetAssetDetails"
                        :fleetAssetOwner="fleetAssetOwnerSummary"
                        :isEdited="isEdited"
                      ></FleetAssetTruckKeyInformation>
                      <FleetAssetTruckIdentifyingInformation
                        v-if="selectedViewType === 'IDI'"
                        :fleetAsset="fleetAssetDetails"
                        :fleetAssetOwner="fleetAssetOwnerSummary"
                        :isEdited="isEdited"
                      ></FleetAssetTruckIdentifyingInformation>
                      <FleetAssetTruckSpecifications
                        v-if="selectedViewType === 'SPC'"
                        :fleetAsset="fleetAssetDetails"
                        :fleetAssetOwner="fleetAssetOwnerSummary"
                        :isEdited="isEdited"
                      ></FleetAssetTruckSpecifications>
                      <FleetAssetTruckComplianceInformation
                        v-if="selectedViewType === 'COM'"
                        :fleetAsset="fleetAssetDetails"
                        :fleetAssetOwnerSummary="fleetAssetOwnerSummary"
                        :isEdited="isEdited"
                      ></FleetAssetTruckComplianceInformation>
                      <FleetAssetTruckImages
                        v-if="selectedViewType === 'IMG'"
                        :fleetAsset="fleetAssetDetails"
                        :isEdited="isEdited"
                      ></FleetAssetTruckImages>
                      <FleetAssetAdditionalEquipment
                        v-if="selectedViewType === 'AEQ'"
                        :key="forceReloadIncrementer"
                        :fleetAsset="fleetAssetDetails"
                        :isEdited="isEdited"
                      ></FleetAssetAdditionalEquipment>
                    </v-flex>
                  </v-layout>
                </v-form>
                <v-flex md12 v-if="!isForm" class="pa-3">
                  <AssociatedClientsTable
                    v-if="selectedViewType === 'AC'"
                    :clientList="relatedClients"
                    :title="`Associated Clients for ${fleetAssetName}`"
                  ></AssociatedClientsTable>
                  <FuelSurchargeLevyDetails
                    v-if="selectedViewType === 'FUEL'"
                    :componentType="FuelComponentType.FLEET_ASSET"
                    key="fleetAssetFuelSurcharge"
                    :entityId="fleetAssetDetails.fleetAssetId"
                  >
                    <ActiveRatesSummary
                      slot="active-rates-summary"
                      serviceRateType="FLEET_ASSET"
                      :fleetAssetDetails="fleetAssetDetails"
                      :currentServiceRate="currentActiveServiceRate"
                      :currentFuelSurcharge="activeFuelSurchargeRate"
                    ></ActiveRatesSummary>
                  </FuelSurchargeLevyDetails>
                  <ServiceRateAdministration
                    v-if="selectedViewType === 'SERV'"
                    :fleetAssetDetails="fleetAssetDetails"
                    :currentActiveFleetServiceRate="currentActiveServiceRate"
                    :hasServiceRateTable="hasServiceRateTable"
                    :isEdited="isEdited"
                    :allFleetAssetServiceRates="allServiceRates"
                    :allFuelSurchargeRates="allFleetAssetFuelSurchargeList"
                    :activeFuelSurchargeId="activeFuelSurchargeId"
                    @refreshServiceRateList="refreshServiceRateList"
                  ></ServiceRateAdministration>
                  <DefaultRatesConfiguration
                    v-if="selectedViewType === 'DEF'"
                    :entityDetails="fleetAssetDetails"
                    :defaultServiceRate="divisionServiceRate"
                    :serviceRateType="RateEntityType.FLEET_ASSET"
                    :allDefaultRates="allDefaultRates"
                  >
                    <ActiveRatesSummary
                      slot="active-rates-summary"
                      serviceRateType="FLEET_ASSET"
                      :fleetAssetDetails="fleetAssetDetails"
                      :currentServiceRate="currentActiveServiceRate"
                      :currentFuelSurcharge="activeFuelSurchargeRate"
                    ></ActiveRatesSummary>
                  </DefaultRatesConfiguration>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex v-else class="app-theme__center-content--body" pa-3>
      <v-layout justify-center>
        <img
          src="@/static/loader/infinity-loader-light.svg"
          height="80px"
          width="80px"
        />
      </v-layout>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import FleetAssetAdditionalEquipment from '@/components/admin/FleetAsset/truck/components/fleet_asset_additional_equipment.vue';
import FleetAssetTruckComplianceInformation from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_compliance_information.vue';
import FleetAssetTruckIdentifyingInformation from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_id_information.vue';
import FleetAssetTruckImages from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_images.vue';
import FleetAssetTruckKeyInformation from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_key_information.vue';
import FleetAssetTruckSpecifications from '@/components/admin/FleetAsset/truck/components/fleet_asset_truck_specifications.vue';
import AssociatedClientsTable from '@/components/common/associated_clients_table.vue';
import FuelSurchargeLevyDetails from '@/components/common/fuel_surcharge_levy_details/fuel_surcharge_levy_details.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import ActiveRatesSummary from '@/components/common/service-rate-table/active_rates_summary/index.vue';
import DefaultRatesConfiguration from '@/components/common/service-rate-table/default-rates-configuration/default_rates_configuration.vue';
import ServiceRateAdministration from '@/components/common/service-rate-table/service_rate_administration/index.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  applyDefaultValuesToFleetAsset,
  isTruckDetails,
} from '@/helpers/FleetAssetHelpers/FleetAssetHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { initialiseFleetAsset } from '@/helpers/classInitialisers/InitialiseFleetAsset';
import { initialiseFleetAssetServiceRate } from '@/helpers/classInitialisers/InitialiseServiceRate';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import FleetAssetComplianceOverrides from '@/interface-models/FleetAsset/FleetAssetComplianceOverrides';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { TruckDetails } from '@/interface-models/FleetAsset/models/FleetAssetTypes/Truck/TruckDetails';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import {
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { FleetAssetFuelSurchargeRate } from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { FuelComponentType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelComponentType';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import RateTableSettings from '@/interface-models/ServiceRates/RateTableSettings';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

interface StatusConfig {
  statusName: string;
  color: string;
}
interface MenuItem {
  id: string;
  title: string;
  isForm: boolean;
  isActive: boolean;
  isHidden: boolean;
}
interface MenuCategory {
  id: string;
  title: string;
  items: MenuItem[];
}
interface FleetAssetData {
  allServiceRates: boolean;
  currentServiceRate: boolean;
  allFuelSurcharges: boolean;
  activeFuelSurcharge: boolean;
  divisionServiceRates: boolean;
}

const props = withDefaults(
  defineProps<{
    isDialog?: boolean;
    fleetAssetOwnerAssociatedFleetAssetIds?: string[];
    fleetAssetOwnerId?: string;
  }>(),
  {
    isDialog: false,
    fleetAssetOwnerAssociatedFleetAssetIds: () => [],
    fleetAssetOwnerId: '',
  },
);

const fleetAssetData = ref<FleetAssetData>({
  allFuelSurcharges: false,
  currentServiceRate: false,
  allServiceRates: false,
  activeFuelSurcharge: false,
  divisionServiceRates: false,
});
const emit = defineEmits<{
  (e: 'addFleetAssetToAssociatedFleetAssets', fleetAssetId: string): void;
}>();

const fleetAssetStore = useFleetAssetStore();
const companyDetailsStore = useCompanyDetailsStore();
const serviceRateStore = useServiceRateStore();
const fuelLevyStore = useFuelLevyStore();

const serviceRateSettings: Ref<any> = ref({});

const forceReloadIncrementer: Ref<number> = ref(0);

const isCancel: Ref<boolean> = ref(false);

const divisionServiceRate: Ref<FleetAssetServiceRate | null> = ref(null);

const currentActiveServiceRate: Ref<FleetAssetServiceRate | null> = ref(null);

const fleetAssetDetails: Ref<FleetAsset | null> = ref(null);

const allServiceRates: Ref<FleetAssetServiceRate[]> = ref([]);

const allFleetAssetFuelSurchargeList: Ref<FleetAssetFuelSurchargeRate[]> = ref(
  [],
);

const uneditedRateTableItems: Ref<RateTableItems[]> = ref([]);

const activeFuelSurchargeId: Ref<string | null | undefined> = ref(null);

const componentTitle: string = 'Truck Administration';

const entityType = ref(EntityType);

const selectedViewType: Ref<string> = ref('KEY');

const hasServiceRateTable: Ref<boolean> = ref(false);
const isEdited: Ref<boolean> = ref(false);
const isAwaitingFleetAssetResponse: Ref<boolean> = ref(false);
const isExpiredFuelSurcharge: Ref<boolean> = ref(false);

const allDefaultRates: Ref<FleetAssetServiceRate[] | null> = ref(null);

const router = useRouter();
const route = useRoute();
const routeId: ComputedRef<string> = computed(() => route.params.id);
const routeName: ComputedRef<string> = computed(() => route.params.name);

const fleetAssetForm = ref<any>(null);

const filteredMenuItems = (items: MenuItem[]) => {
  return items.filter((item) => !item.isHidden);
};

const serviceTypeRate: ComputedRef<ServiceTypeRates[]> = computed(() => {
  return serviceTypeRates.filter(
    (rateType: ServiceTypeRates) => !rateType.adhoc,
  );
});

const selectedFleetAssetId: ComputedRef<string | null> = computed(() => {
  return fleetAssetStore.selectedFleetAssetId;
});

watch(selectedFleetAssetId, () => {
  findAndSetSelectedFleetAssetDetails();
});

function setSettings() {
  if (!currentActiveServiceRate.value) {
    serviceRateSettings.value = [];
    return;
  }
  const settings: any = {};
  const serviceTypeList = companyDetailsStore.getServiceTypesList;
  for (let i = 0; i < serviceTypeList.length; i++) {
    const serviceId = serviceTypeList[i].serviceTypeId;
    const newProperty = 'serviceId-' + serviceId;
    settings[newProperty] = [];
    const serviceArray: RateTableSettings[] = [];
    for (let r = 0; r < serviceTypeRate.value.length; r++) {
      const rateId = serviceTypeRate.value[r].rateTypeId;
      const existingServiceRate =
        currentActiveServiceRate.value.rateTableItems.find(
          (x: RateTableItems) =>
            x.serviceTypeId === serviceId && x.rateTypeId === rateId,
        );
      if (existingServiceRate) {
        serviceArray.push(new RateTableSettings(serviceId, 2, rateId));
      } else {
        const selectedOption = rateId === 1 ? 3 : 3;
        serviceArray.push(
          new RateTableSettings(serviceId, selectedOption, rateId),
        );
      }
    }
    settings[newProperty] = serviceArray;
  }
  serviceRateSettings.value = settings;
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

function goToSubcontractorIndexRoute() {
  router.push({
    name: 'Subcontractor',
  });
}

const isAdmin = computed(() => {
  return hasAdminRole();
});

const isNewFleetAsset: ComputedRef<boolean> = computed(() => {
  return route.params.id === 'new' || selectedFleetAssetId.value === 'new';
});

// Find the associated Fleet Asset from the list in the store. Re-initialise it
// to create a deep clone, then save to local working copy
async function findAndSetSelectedFleetAssetDetails(
  isCancelNewFleetAsset: boolean = false,
) {
  if (!props.isDialog && selectedFleetAssetId.value !== routeId.value) {
    fleetAssetStore.setSelectedFleetAssetId(routeId.value);
  }

  isCancel.value = true;
  isEdited.value = false;
  // If a new fleet asset is canceled we should push the user back to the main subcontractor index
  if (isCancelNewFleetAsset && isNewFleetAsset.value) {
    if (!props.isDialog) {
      goToSubcontractorIndexRoute();
    } else {
      closeFleetAssetDialog();
    }
    return;
  }

  // reset any validation issues on inputs.
  if (fleetAssetForm.value) {
    fleetAssetForm.value.resetValidation();
  }

  // if the fleet asset is new we initialise a new driver.
  if (isNewFleetAsset.value) {
    fleetAssetDetails.value = initialiseFleetAsset(new FleetAsset());
    // Add any default values from DivisionDetails custom config
    applyDefaultValuesToFleetAsset(
      fleetAssetDetails.value,
      useCompanyDetailsStore().divisionDetails,
    );
    if (fleetAssetDetails.value) {
      fleetAssetDetails.value.fleetAssetOwnerId = props.fleetAssetOwnerId;
      fleetAssetDetails.value.fleetAssetTypeId = 1;
      fleetAssetDetails.value.fleetAssetTypeObject = new TruckDetails();
    }
    isEdited.value = true;
    return;
  }

  // Confirm that the selected fleet asset is known locally
  const fleetAssetSummary: FleetAssetSummary | undefined =
    fleetAssetStore.getFleetAssetFromFleetAssetId(selectedFleetAssetId.value);

  if (!fleetAssetSummary) {
    handleUnknownFleetAsset();
    return;
  }

  // Request full FleetAsset object. Rates will be requested in the handler of
  // the fleet asset response.
  if (selectedFleetAssetId.value) {
    const fleetAsset: FleetAsset | null =
      await fleetAssetStore.requestFleetAssetByFleetAssetId(
        selectedFleetAssetId.value,
      );
    setFleetAssetFromResponse(fleetAsset);
  }
}

// Handles response of full FleetAsset after selection of a fleetAssetId
function setFleetAssetFromResponse(fleetAsset: FleetAsset | null) {
  if (!fleetAsset?.fleetAssetId) {
    handleUnknownFleetAsset();
    return;
  }
  fleetAssetDetails.value = fleetAsset;
  // Request the fleet assets rates.
  getFleetAssetRates(fleetAsset.fleetAssetId);
  forceReloadIncrementer.value++;
}
// If the selected fleet asset was not found, we should alert and push the user back to the subcontractor index page.
function handleUnknownFleetAsset() {
  showAppNotification('Sorry, we could not find that Fleet Asset.');
  fleetAssetDetails.value = null;
  fleetAssetStore.setSelectedFleetAssetId(null);
  closeFleetAssetDialog();
  router.push({ name: 'Subcontractor' });
}

// Return the FleetAssetOwner object for the current FleetAsset
const fleetAssetOwnerSummary: ComputedRef<FleetAssetOwnerSummary | undefined> =
  computed(() => {
    const ownerId = fleetAssetDetails.value
      ? fleetAssetDetails.value.fleetAssetOwnerId
      : '';
    return useFleetAssetOwnerStore().getOwnerFromOwnerId(ownerId);
  });

// Return a readable representation of the id/rego to use as a title
const fleetAssetName = computed<string>(() => {
  if (!fleetAssetDetails.value) {
    return '';
  }
  const regoNumber = fleetAssetDetails.value.isTruck
    ? (fleetAssetDetails.value.fleetAssetTypeObject as TruckDetails)
        .registrationDetails.registrationNumber
    : '';
  return regoNumber
    ? `${fleetAssetDetails.value.csrAssignedId} (${regoNumber})`
    : `${fleetAssetDetails.value.csrAssignedId}`;
});

function closeFleetAssetDialog(): void {
  fleetAssetStore.setSelectedFleetAssetId(null);
}

const menuOptions: ComputedRef<MenuCategory[]> = computed(() => {
  const truckInfo: MenuCategory = {
    id: 'INFO',
    title: 'Truck Info',
    items: [
      {
        id: 'KEY',
        title: 'Key Details',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: false,
      },
      {
        id: 'IDI',
        title: 'Identifying Information',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: isOutsideHire.value,
      },
      {
        id: 'SPC',
        title: 'Truck Specifications',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: isOutsideHire.value,
      },
      {
        id: 'COM',
        title: 'Compliance',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: isOutsideHire.value,
      },
      {
        id: 'IMG',
        title: 'Truck Images',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: isOutsideHire.value,
      },
      {
        id: 'AEQ',
        title: 'Additional Equipment',
        isForm: true,
        isActive: isEdited.value ? false : true,
        isHidden: isOutsideHire.value,
      },
      {
        id: 'AC',
        title: 'Associated Clients',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isHidden: false,
      },
    ],
  };

  const rateInfo: MenuCategory = {
    id: 'RATES',
    title: 'Rate Details',
    items: [
      {
        id: 'SERV',
        title: 'Vehicle Service Rates',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isHidden: false,
      },
      {
        id: 'DEF',
        title: 'Division Service Rates',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isHidden: false,
      },
      {
        id: 'FUEL',
        title: 'Fuel Surcharge',
        isForm: false,
        isActive: isEdited.value ? false : true,
        isHidden: false,
      },
    ],
  };

  return [truckInfo, rateInfo];
});

const isForm = computed(() => {
  const selectedView = menuOptions.value
    .flatMap((x) => x.items)
    .find((x: MenuItem) => x.id === selectedViewType.value);
  return selectedView ? selectedView.isForm : false;
});

// Find clients that have this fleet asset in their preAssignedVehicleDetails
const relatedClients = computed(() => {
  if (!fleetAssetDetails.value?.fleetAssetId) {
    return [];
  }

  return useClientDetailsStore().clientSummaryList.filter(
    (client: ClientSearchSummary) =>
      client.preAssignedVehicleDetails?.includes(
        fleetAssetDetails.value!.fleetAssetId,
      ),
  );
});

const summaryInfoList = computed(() => {
  const infoList: KeyValuePair[] = [];

  if (!fleetAssetDetails.value) {
    return infoList;
  }

  const truckDetails: TruckDetails = fleetAssetDetails.value
    .fleetAssetTypeObject as TruckDetails;

  if (!truckDetails) {
    return infoList;
  }

  infoList.push({
    id: 'subcontractorName',
    title: 'Subcontractor (owner)',
    value: fleetAssetOwnerSummary.value
      ? fleetAssetOwnerSummary.value.name
      : '-',
  });

  const defaultDriver: DriverDetailsSummary | undefined =
    truckDetails.defaultDriver
      ? useDriverDetailsStore().getDriverFromDriverId(
          truckDetails.defaultDriver,
        )
      : undefined;
  if (isOutsideHire.value) {
    infoList.push({
      id: 'outsideHire',
      title: 'Outside Hire',
      value: 'YES',
    });
  }
  infoList.push({
    id: 'defaultDriver',
    title: 'Default Driver',
    value: defaultDriver ? defaultDriver.displayName : '-',
  });
  if (!isOutsideHire.value) {
    infoList.push({
      id: 'regoNumber',
      title: 'Registration Number',
      value: truckDetails.registrationDetails.registrationNumber
        ? truckDetails.registrationDetails.registrationNumber
        : '-',
    });
    infoList.push({
      id: 'regoExpiry',
      title: 'Registration Expiry',
      value: truckDetails.registrationDetails.expiry
        ? returnFormattedDate(truckDetails.registrationDetails.expiry)
        : '-',
    });
    infoList.push({
      id: 'truckClass',
      title: 'Vehicle Class',
      value: truckDetails.truckClass
        ? returnTruckClassFromShortName(truckDetails.truckClass)
        : '-',
    });
  }
  infoList.push({
    id: 'ownerName',
    title: 'Owner',
    value: fleetAssetOwnerSummary.value
      ? fleetAssetOwnerSummary.value.name
      : '-',
  });
  infoList.push({
    id: 'serviceRates',
    title: 'Active Service Rate',
    value: hasServiceRateTable.value ? 'YES' : 'NO',
  });
  infoList.push({
    id: 'fuelSurcharge',
    title: 'Active Fuel Surcharge',
    value: !isExpiredFuelSurcharge.value ? 'YES' : 'NO',
  });

  if (isAdmin.value) {
    infoList.push({
      id: 'ID',
      title: 'fleetAssetId',
      value: fleetAssetDetails.value.fleetAssetId,
    });
  }

  return infoList;
});

// Finds a ServiceTypes object for the provided shortServiceTypeName, and
// returns the optionSelectName. Used in the summary list in the html
const returnTruckClassFromShortName = (id: string): string => {
  const serviceWeights: ServiceTypes[] =
    companyDetailsStore.getServiceTypesList.filter(
      (s) => s.longServiceTypeName !== 'UNIT RATE',
    );
  const foundClass = serviceWeights.find((s) => s.shortServiceTypeName === id);
  return foundClass ? foundClass.optionSelectName : '';
};

// take user to fleet asset owner page
function viewFleetAssetOwner(): void {
  if (!fleetAssetOwnerSummary.value) {
    return;
  }
  useFleetAssetOwnerStore().setSelectedFleetAssetOwnerId(
    fleetAssetOwnerSummary.value.ownerId,
  );
  router.push({
    name: 'Owner',
    params: {
      name: fleetAssetOwnerSummary.value.name.toLowerCase().replace(/ /g, '-'),
      id: fleetAssetOwnerSummary.value.ownerId,
    },
  });
}

// Set the selected view from the left navigation menu
function setSelectedView(id: string) {
  if (!id) {
    selectedViewType.value = menuOptions.value[0].id;
    return;
  }
  selectedViewType.value = id;
}

const currentStatus = computed<StatusConfig>(() => {
  const statusConfig = {
    statusName: '',
    color: '',
  };

  if (!fleetAssetDetails.value) {
    return statusConfig;
  }

  const doNotUse: boolean = fleetAssetDetails.value.statusList.includes(47);
  const active: boolean = fleetAssetDetails.value.statusList.includes(4);

  if (doNotUse) {
    statusConfig.statusName = 'DO NOT USE';
    statusConfig.color = 'error';
  } else if (active) {
    statusConfig.statusName = 'Active';
    statusConfig.color = 'success';
  } else {
    statusConfig.statusName = 'INCOMPLETE';
    statusConfig.color = 'error';
  }
  return statusConfig;
});

// The current active FuelSurchargeRate as defined by the
// activeFuelSurchargeId. Passed into the ActiveRatesSummary component
const activeFuelSurchargeRate: ComputedRef<FuelSurchargeRate | null> = computed(
  () => {
    const activeId = activeFuelSurchargeId.value;
    if (activeId === null) {
      return null;
    }
    const foundActive = allFleetAssetFuelSurchargeList.value.find(
      (r) => r.id === activeFuelSurchargeId.value,
    );
    return foundActive ? foundActive : null;
  },
);

// Handle response to the request for all of the Fleet Asset's Service Rates
const setAllFleetAssetServiceRatesList = (
  rateList: FleetAssetServiceRate[],
) => {
  const serviceRates = rateList.reduce(
    (accumulator: FleetAssetServiceRate[], value: FleetAssetServiceRate) => {
      const nextIndex = accumulator.findIndex(
        (i: FleetAssetServiceRate) =>
          value.validToDate &&
          i.validToDate &&
          value.validToDate > i.validToDate,
      );
      const index = nextIndex > -1 ? nextIndex : accumulator.length;
      accumulator.splice(index, 0, value);
      return accumulator;
    },
    [],
  );
  allServiceRates.value = serviceRates;
};

/**
 * Validates the form then saves the full Fleet Asset document if all required
 * fields have been completed.
 */
async function saveFleetAssetDetails(): Promise<void> {
  if (!fleetAssetDetails.value) {
    return;
  }
  if (!fleetAssetForm.value?.validate()) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  if (!fleetAssetOwnerSummary.value) {
    showAppNotification('Could not find an associated Fleet Asset Owner.');
    return;
  }
  // Set outside hire property based on owner affiliation
  fleetAssetDetails.value.outsideHire =
    fleetAssetOwnerSummary.value.isOutsideHire;
  if (fleetAssetDetails.value.outsideHire) {
    const complianceOverrides: FleetAssetComplianceOverrides = {
      insuranceRequired: false,
      registrationRequired: false,
      equipmentServiceRequired: false,
    };
    // Set compliance overrides if it is an outside hire
    fleetAssetDetails.value.complianceOverrides = fleetAssetDetails.value
      .complianceOverrides
      ? fleetAssetDetails.value.complianceOverrides
      : complianceOverrides;
  }

  // Dispatch save request
  isAwaitingFleetAssetResponse.value = true;
  const fleetAsset = await fleetAssetDetails.value.save();
  isAwaitingFleetAssetResponse.value = false;
  // Handle result of save operation
  if (fleetAsset) {
    fleetAssetDetails.value = fleetAsset;
    handleSuccessfullySavedFleetAsset();
    forceReloadIncrementer.value++;
    isEdited.value = false;
  } else {
    showAppNotification(
      'An error occurred while saving the Fleet Asset.',
      HealthLevel.ERROR,
    );
  }
}

// Return true if the owner is of affiliation '3', corresponding with Outside
// Hire. Use this to set outsideHire property in Fleet Asset, as well as to set
// required fields
const isOutsideHire: ComputedRef<boolean> = computed(() => {
  return fleetAssetOwnerSummary.value?.isOutsideHire ?? false;
});

// Set the component state to edit mode
function editFleetAssetDetails() {
  isEdited.value = true;
  isCancel.value = false;
}

// Handle emit from Service Rate Index component to force refresh of rates
// table
function refreshServiceRateList() {
  if (fleetAssetStore.selectedFleetAssetId) {
    let fleetAssetId = fleetAssetStore.selectedFleetAssetId;
    if (fleetAssetId === 'new' && fleetAssetDetails.value) {
      fleetAssetId = fleetAssetDetails.value.fleetAssetId;
    }
    getFleetAssetRates(fleetAssetId);
  }
}

// Set awaiting variables to false and request all service rates and fuel
// surcharges
async function getFleetAssetRates(fleetAssetId: string) {
  fleetAssetData.value.activeFuelSurcharge = false;
  fleetAssetData.value.allFuelSurcharges = false;
  fleetAssetData.value.allServiceRates = false;
  fleetAssetData.value.currentServiceRate = false;

  const searchDate = moment().valueOf();
  const [
    activeServiceRate,
    allServiceRates,
    allFuelSurcharges,
    activeFuelLevy,
  ] = await Promise.all([
    serviceRateStore.getActiveFleetAssetServiceRates(fleetAssetId, searchDate),
    serviceRateStore.getAllServiceRatesForFleetAssetId(fleetAssetId),
    fuelLevyStore.getAllFleetAssetFuelSurchargeRates(fleetAssetId),
    fuelLevyStore.getCurrentFleetAssetFuelSurchargeRates(
      fleetAssetId,
      searchDate,
    ),
  ]);
  // Delay request for division default rates, to prevent race condition with
  // fleet-specific response
  const allDefaultDivisionRates =
    await serviceRateStore.getAllDivisionDefaultFleetAssetServiceRates();

  // Handle division rate card response
  divisionServiceRate.value = activeServiceRate ?? null;
  fleetAssetData.value.divisionServiceRates = true;
  allDefaultRates.value = allDefaultDivisionRates;
  handleActiveServiceRate(activeServiceRate);
  handleServiceRateList(allServiceRates);
  handleFuelSurchargeList(allFuelSurcharges);
  handleCurrentFleetAssetFuelSurcharge(activeFuelLevy);
}

/**
 * Process the response for the 'getCurrentFleetAssetFuelSurchargeRates' API
 * and set local variables based on result called in getFleetAssetRates
 * @param fuelSurcharges the current active fuel surcharge for the Fleet Asset
 */
function handleCurrentFleetAssetFuelSurcharge(
  fuelSurcharges: FleetAssetFuelSurchargeRate[] | null,
) {
  isExpiredFuelSurcharge.value = !fuelSurcharges?.length ? true : false;
  activeFuelSurchargeId.value = fuelSurcharges ? fuelSurcharges[0].id : null;
  if (!fuelSurcharges) {
    if (
      !(fleetAssetDetails.value as FleetAsset)
        .expiredFuelSurchargeDefaultsToDivisionRate
    ) {
      showAppNotification(
        'Fleet asset currently has no active Fuel Surcharge. Please create one.',
      );
    }
  }
  fleetAssetData.value.activeFuelSurcharge = true;
}
/**
 * Process the response for the 'selectedAllFleetAssetFuelSurchargeRatesList'
 * API, which fetches all Fuel Surcharges for the Fleet Asset
 * @param allFuelSurcharges the full list of Fuel Surcharge documents for the
 * fleet asset
 */
function handleFuelSurchargeList(
  allFuelSurcharges: FleetAssetFuelSurchargeRate[] | null,
) {
  if (!fleetAssetData.value.allFuelSurcharges) {
    allFleetAssetFuelSurchargeList.value = allFuelSurcharges ?? [];
    fleetAssetData.value.allFuelSurcharges = true;
  }
}

/**
 * Process the response for the 'selectedAllFleetAssetServiceRatesList' API,
 * which fetches all Service Rates for the Fleet Asset
 * @param responseList the full list of Service Rate documents for the
 * fleet asset
 */
function handleServiceRateList(responseList: FleetAssetServiceRate[] | null) {
  if (responseList) {
    if (responseList[0]?.fleetAssetId === selectedFleetAssetId.value) {
      setAllFleetAssetServiceRatesList(responseList);
    } else {
      allServiceRates.value = responseList;
    }
  }
  fleetAssetData.value.allServiceRates = true;
}

/**
 * Process the response for the 'getActiveFleetAssetServiceRates' API, which
 * fetches the current active Service Rate for the Fleet Asset
 * @param activeServiceRate the current active service rate for the Fleet Asset
 */
function handleActiveServiceRate(
  activeServiceRate: FleetAssetServiceRate | null,
) {
  fleetAssetData.value.currentServiceRate = true;
  const serviceRateResponse: FleetAssetServiceRate | null = activeServiceRate;
  hasServiceRateTable.value = !serviceRateResponse ? false : true;
  currentActiveServiceRate.value = !serviceRateResponse
    ? null
    : serviceRateResponse;
  currentActiveServiceRate.value = !serviceRateResponse
    ? null
    : initialiseFleetAssetServiceRate(serviceRateResponse);
  if (!serviceRateResponse) {
    // We don't need to show a notification if the FleetAsset is set to use
    // division rates on expired rate card
    if (
      !(fleetAssetDetails.value as FleetAsset)
        .expiredServiceRateDefaultsToDivisionRate
    ) {
      showAppNotification('Fleet asset currently has no active service rates.');
    }
    return;
  }
  uneditedRateTableItems.value = serviceRateResponse.rateTableItems;
  setSettings();
}

// When the save events are finished we should update route information and associations
const handleSuccessfullySavedFleetAsset = () => {
  if (!fleetAssetDetails.value) {
    return;
  }

  showAppNotification('Fleet asset successfully saved.', HealthLevel.SUCCESS);
  // update route and subcontractor search selection
  if (!props.isDialog) {
    if (
      routeName.value !==
        fleetAssetDetails.value.csrAssignedId
          .toLowerCase()
          .replace(/ /g, '-') &&
      routeId.value !== fleetAssetDetails.value.fleetAssetId
    ) {
      router.push({
        name: 'Truck',
        params: {
          name: fleetAssetDetails.value.csrAssignedId
            .toLowerCase()
            .replace(/ /g, '-'),
          id: fleetAssetDetails.value.fleetAssetId,
        },
      });
    }
    return;
  }
  // if the driver was saved within the fleet asset owner we need to associate the saved driver with the fleet asset owner if it is not already;
  emit(
    'addFleetAssetToAssociatedFleetAssets',
    fleetAssetDetails.value.fleetAssetId,
  );
};

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

/**
 * Called by mitt listener when a Fleet Asset save event is received. Used to
 * reset the component state if the Fleet Asset has been updated by another
 * user.
 */
function handleExternalSave(incomingFleetAsset: FleetAsset | null): void {
  if (!incomingFleetAsset?.fleetAssetId) {
    return;
  }
  if (
    incomingFleetAsset.fleetAssetId === fleetAssetDetails.value?.fleetAssetId &&
    !isAwaitingFleetAssetResponse.value
  ) {
    // Show notification if currently in edit mode
    if (isEdited.value) {
      showAppNotification(
        `${fleetAssetName.value} has been updated by another user. Your changes have been discarded.`,
        HealthLevel.WARNING,
      );
    }
    isEdited.value = false;
    findAndSetSelectedFleetAssetDetails();
  }
}

useMittListener('selectedFleetAssetDetails', handleExternalSave);

// Init the component at a specific tab if the value exists in the FleetAssetStore
onBeforeMount(() => {
  if (fleetAssetStore.selectedFleetAssetView) {
    selectedViewType.value = fleetAssetStore.selectedFleetAssetView;
    fleetAssetStore.setSelectedFleetAssetView(null);
  } else {
    selectedViewType.value = 'KEY';
  }
});

onMounted(() => {
  findAndSetSelectedFleetAssetDetails();
});

onBeforeUnmount(() => {
  fleetAssetStore.setSelectedFleetAssetId(null);
});
</script>
