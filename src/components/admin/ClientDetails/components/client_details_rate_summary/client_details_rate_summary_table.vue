<template>
  <section class="service-rate-table-read-only">
    <!-- Loading State -->
    <div
      v-if="isLoadingServiceRate || isLoadingVariations"
      class="text-center py-4"
    >
      <v-progress-circular
        indeterminate
        color="primary"
        size="40"
      ></v-progress-circular>
      <p class="mt-2 text-muted">Loading service rates and variations...</p>
    </div>

    <!-- No Data State -->
    <v-alert
      v-else-if="!serviceRate || !serviceRate.rateTableItems.length"
      :value="true"
      color="info"
      icon="info"
      class="mb-3"
    >
      No service rates are currently configured.
    </v-alert>

    <div v-else>
      <v-layout justify-space-between align-center>
        <!-- Service Rate Type -->
        <!-- Rate Type Navigation Buttons -->
        <v-flex
          v-if="serviceRate && serviceRate.rateTableItems.length > 0"
          class="button-group"
        >
          <span
            v-for="rateType in availableRateTypes"
            :key="rateType.rateTypeId"
          >
            <v-btn
              :class="{
                'v-btn--active': selectedRateTypeId === rateType.rateTypeId,
              }"
              flat
              @click="selectedRateTypeId = rateType.rateTypeId"
            >
              <span class="px-2">
                <strong>{{ rateType.longName.toUpperCase() }} RATES</strong>
              </span>
            </v-btn>
          </span>
        </v-flex>
        <!-- Rate Table Summary Card -->
        <v-flex
          v-if="
            rateTableSummary.customTables.length > 0 ||
            rateTableSummary.divisionTables.length > 0
          "
          class="rate-table-summary-card"
          md3
        >
          <h2 class="text-muted pb-2">Applied Rate Tables:</h2>
          <v-flex>
            <v-flex
              v-if="rateTableSummary.customTables.length > 0"
              class="mb-1"
            >
              <strong class="custom-rates-label">Custom Rate Table:</strong>
              <span class="ml-2">{{
                rateTableSummary.customTables.join(', ')
              }}</span>
            </v-flex>

            <v-flex v-if="rateTableSummary.divisionTables.length > 0">
              <strong class="division-rates-label">Division Rate Table:</strong>
              <span class="ml-2">{{
                rateTableSummary.divisionTables.join(', ')
              }}</span>
            </v-flex>
          </v-flex>
        </v-flex>
      </v-layout>
      <!-- tables -->
      <div
        v-if="
          !isLoadingServiceRate &&
          !isLoadingVariations &&
          serviceRate &&
          serviceRate.rateTableItems.length > 0
        "
      >
        <!-- Time Rate Table -->
        <div
          v-if="
            selectedRateTypeId === JobRateType.TIME && timeRateItems.length > 0
          "
        >
          <v-data-table
            class="default-table-dark gd-dark-theme connected-rows-table"
            :headers="timeHeaders"
            :items="timeRateItems"
            hide-actions
          >
            <template v-slot:items="slotProps">
              <tr>
                <td>
                  <span class="service-name">{{
                    returnServiceTypeLongNameFromId(
                      slotProps.item.serviceTypeId,
                    )
                  }}</span>
                </td>
                <td>
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.rateTypeObject.rate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{
                          formatRateWithVariation(
                            slotProps.item,
                            slotProps.item.rateTypeObject.rate,
                          )
                        }}
                        <span
                          v-if="slotProps.item.rateTypeObject.rate > 0"
                          class="rate-variation-symbol"
                          >{{ getRateVariationSymbol(slotProps.item) }}</span
                        >
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.rateTypeObject.rate,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{
                      formatRateWithVariation(
                        slotProps.item,
                        slotProps.item.rateTypeObject.rate,
                      )
                    }}
                  </span>
                </td>
                <td>
                  {{ slotProps.item.rateTypeObject.minCharge }}
                  mins
                </td>
                <td>
                  {{ slotProps.item.rateTypeObject.chargeIncrement }} mins
                </td>
                <td>
                  {{
                    returnGraceShortName(
                      slotProps.item.rateTypeObject.graceType,
                    )
                  }}
                </td>
                <td>
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.rateTypeObject.standbyRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{
                          formatRateWithVariation(
                            slotProps.item,
                            slotProps.item.rateTypeObject.standbyRate,
                          )
                        }}
                        <span
                          v-if="slotProps.item.rateTypeObject.standbyRate > 0"
                          class="rate-variation-symbol"
                          >{{ getRateVariationSymbol(slotProps.item) }}</span
                        >
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.rateTypeObject.standbyRate,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{
                      formatRateWithVariation(
                        slotProps.item,
                        slotProps.item.rateTypeObject.standbyRate,
                      )
                    }}
                  </span>
                </td>
                <td>
                  {{
                    formatFuelSurcharge(
                      slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                    )
                  }}
                </td>
                <td>
                  <span
                    class="source-badge"
                    :class="`source-${determineRateSource(
                      slotProps.item.originalRateTableMongoId,
                    ).toLowerCase()}`"
                  >
                    {{
                      determineRateSource(
                        slotProps.item.originalRateTableMongoId,
                      )
                    }}
                  </span>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>

        <!-- Zone Rate Table -->
        <div v-if="selectedRateTypeId === JobRateType.ZONE">
          <v-data-table
            class="default-table-dark gd-dark-theme connected-rows-table"
            :headers="zoneHeaders"
            :items="zoneRateTableData"
            hide-actions
            :rows-per-page-items="[10, 20]"
          >
            <template v-slot:items="slotProps">
              <tr
                :class="{
                  'service-header-row': slotProps.item.isServiceHeader,
                  'zone-child-row': slotProps.item.isRangeRow,
                }"
              >
                <td>
                  <span
                    v-if="slotProps.item.isServiceHeader"
                    class="service-name"
                  >
                    {{ slotProps.item.serviceTypeName }}
                  </span>
                  <span v-else class="zone-child-text">
                    {{ slotProps.item.serviceTypeName }}
                  </span>
                </td>
                <td>
                  <template v-if="!slotProps.item.isServiceHeader">
                    <v-tooltip
                      bottom
                      v-if="
                        showRateVariationsHighlight && slotProps.item.rate > 0
                      "
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on" class="rate-text">
                          {{ slotProps.item.zoneRate }}
                          <span
                            v-if="slotProps.item.rate > 0"
                            class="rate-variation-symbol"
                            >{{ getRateVariationSymbol(slotProps.item) }}</span
                          >
                        </span>
                      </template>
                      <span>{{
                        getRateTooltip(slotProps.item, slotProps.item.rate)
                      }}</span>
                    </v-tooltip>
                    <span v-else class="rate-text">
                      {{ slotProps.item.zoneRate }}
                    </span>
                  </template>
                </td>
                <td>
                  <v-tooltip
                    bottom
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.additionalPickUpFlagFall > 0
                    "
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.pickupFlagfall }}
                        <span
                          v-if="slotProps.item.additionalPickUpFlagFall > 0"
                          class="rate-variation-symbol"
                          >{{ getRateVariationSymbol(slotProps.item) }}</span
                        >
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.additionalPickUpFlagFall,
                        slotProps.item.pickupFlagfall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.pickupFlagfall }}
                  </span>
                </td>
                <td>
                  <v-tooltip
                    bottom
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.additionalDropOffFlagFall > 0
                    "
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.dropoffFlagfall }}
                        <span
                          v-if="slotProps.item.additionalDropOffFlagFall > 0"
                          class="rate-variation-symbol"
                          >{{ getRateVariationSymbol(slotProps.item) }}</span
                        >
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.additionalDropOffFlagFall,
                        slotProps.item.dropoffFlagfall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.dropoffFlagfall }}
                  </span>
                </td>
                <td>{{ slotProps.item.calculation }}</td>
                <td
                  v-if="
                    shouldApplyRateVariationsToDemurrage &&
                    slotProps.item.demurrage > 0
                  "
                >
                  <template v-if="!slotProps.item.isServiceHeader">
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on" class="demurrage-text">
                          {{ slotProps.item.demurrageRate }}
                          <span class="rate-variation-symbol">{{
                            getRateVariationSymbol(slotProps.item)
                          }}</span>
                        </span>
                      </template>
                      <span>{{
                        getRateTooltip(
                          slotProps.item,
                          slotProps.item.demurrage,
                          slotProps.item.demurrageRate,
                        )
                      }}</span>
                    </v-tooltip>
                  </template>
                </td>
                <td v-else class="demurrage-text">
                  {{
                    slotProps.item.demurrageRate
                      ? `$${slotProps.item.demurrageRate}`
                      : ''
                  }}
                </td>
                <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
                <td>
                  <span
                    v-if="!slotProps.item.isServiceHeader"
                    class="source-badge"
                    :class="`source-${determineRateSource(
                      slotProps.item.rateTableItem.originalRateTableMongoId,
                    ).toLowerCase()}`"
                  >
                    {{
                      determineRateSource(
                        slotProps.item.rateTableItem.originalRateTableMongoId,
                      )
                    }}
                  </span>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>

        <!-- Distance Rate Table -->
        <div
          v-if="
            selectedRateTypeId === JobRateType.DISTANCE &&
            distanceRateItems.length > 0
          "
        >
          <v-data-table
            class="default-table-dark gd-dark-theme connected-rows-table"
            :headers="distanceHeaders"
            :items="distanceRateTableData"
            hide-actions
          >
            <template v-slot:items="slotProps">
              <tr
                :class="{
                  'distance-child-row': slotProps.item.isDistanceRow,
                  'range-row': slotProps.item.isRangeRow,
                }"
              >
                <td>
                  <span
                    v-if="slotProps.item.isRangeRow"
                    class="distance-child-text"
                  >
                    {{ slotProps.item.serviceTypeName }}
                  </span>
                  <span v-else class="service-name">
                    {{ slotProps.item.serviceTypeName }}
                  </span>
                </td>
                <td>
                  <template v-if="!slotProps.item.isRangeRow">
                    <v-tooltip
                      v-if="
                        showRateVariationsHighlight &&
                        slotProps.item.rateTypeObject?.baseFreightCharge > 0
                      "
                      bottom
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on" class="rate-text">
                          {{ slotProps.item.baseFreight }}
                          <span
                            v-if="
                              slotProps.item.rateTypeObject?.baseFreightCharge >
                              0
                            "
                            class="rate-variation-symbol"
                            >{{ getRateVariationSymbol(slotProps.item) }}</span
                          >
                        </span>
                      </template>
                      <span>{{
                        getRateTooltip(
                          slotProps.item,
                          slotProps.item.rateTypeObject.baseFreightCharge,
                        )
                      }}</span>
                    </v-tooltip>
                    <span v-else class="rate-text">
                      {{ slotProps.item.baseFreight }}
                    </span>
                  </template>
                </td>
                <td>
                  {{
                    !slotProps.item.isRangeRow ? slotProps.item.calculation : ''
                  }}
                </td>
                <td>
                  {{
                    !slotProps.item.isRangeRow ? slotProps.item.increment : ''
                  }}
                </td>
                <td>
                  {{ !slotProps.item.isRangeRow ? slotProps.item.legs : '' }}
                </td>
                <td class="text-center">
                  {{
                    !slotProps.item.isRangeRow
                      ? slotProps.item.minimumCharge
                      : ''
                  }}
                </td>
                <td
                  v-if="
                    shouldApplyRateVariationsToDemurrage &&
                    slotProps.item.demurrage > 0
                  "
                >
                  <template
                    v-if="
                      !slotProps.item.isServiceHeader &&
                      !slotProps.item.isRangeRow
                    "
                  >
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on" class="demurrage-text">
                          {{ slotProps.item.demurrageRate }}
                          <span class="rate-variation-symbol">{{
                            getRateVariationSymbol(slotProps.item)
                          }}</span>
                        </span>
                      </template>
                      <span>{{
                        getRateTooltip(
                          slotProps.item,
                          slotProps.item.demurrage,
                          slotProps.item.demurrageRate,
                        )
                      }}</span>
                    </v-tooltip>
                  </template>
                </td>
                <td v-else class="demurrage-text">
                  {{
                    slotProps.item.demurrageRate
                      ? `$${slotProps.item.demurrageRate}`
                      : ''
                  }}
                </td>
                <td>{{ slotProps.item.rangeText }}</td>
                <td>
                  <template v-if="slotProps.item.range">
                    <v-tooltip
                      v-if="
                        showRateVariationsHighlight &&
                        slotProps.item.rangeRate.rate > 0
                      "
                      bottom
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on" class="rate-text">
                          {{ slotProps.item.range }}
                          <span
                            v-if="
                              slotProps.item.rangeRate &&
                              slotProps.item.rangeRate.rate > 0
                            "
                            class="rate-variation-symbol"
                            >{{
                              getRateVariationSymbol(
                                slotProps.item.rateTableItem,
                              )
                            }}</span
                          >
                        </span>
                      </template>
                      <span>{{
                        getRateTooltip(
                          slotProps.item.rateTableItem,
                          slotProps.item.rangeRate.rate,
                        )
                      }}</span>
                    </v-tooltip>
                    <span v-else class="rate-text">{{
                      slotProps.item.range
                    }}</span>
                  </template>
                </td>
                <td>
                  <span
                    v-if="!slotProps.item.isRangeRow"
                    class="source-badge"
                    :class="`source-${determineRateSource(
                      slotProps.item.rateTableItem.originalRateTableMongoId,
                    ).toLowerCase()}`"
                  >
                    {{
                      determineRateSource(
                        slotProps.item.rateTableItem.originalRateTableMongoId,
                      )
                    }}
                  </span>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>

        <!-- Point-to-Point Rate Table -->
        <div v-if="selectedRateTypeId === JobRateType.POINT_TO_POINT">
          <v-data-table
            class="default-table-dark gd-dark-theme connected-rows-table"
            :headers="pointToPointHeaders"
            :items="pointToPointRateTableData"
            hide-actions
          >
            <template v-slot:items="slotProps">
              <tr>
                <td>
                  <span class="service-name">{{
                    slotProps.item.serviceTypeName
                  }}</span>
                </td>
                <td>{{ slotProps.item.fromAddress }}</td>
                <td>{{ slotProps.item.toAddress }}</td>
                <td>
                  <v-tooltip v-if="slotProps.item.baseRate > 0" bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.rate }}
                        <span
                          v-if="slotProps.item.rateTypeObject.baseRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(slotProps.item, slotProps.item.baseRate)
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.rate }}
                  </span>
                </td>
                <td
                  v-if="
                    shouldApplyRateVariationsToDemurrage &&
                    slotProps.item.demurrage > 0
                  "
                >
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="demurrage-text">
                        {{ slotProps.item.demurrageRate }}
                        <span class="rate-variation-symbol">{{
                          getRateVariationSymbol(slotProps.item)
                        }}</span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.demurrage,
                        slotProps.item.demurrageRate,
                      )
                    }}</span>
                  </v-tooltip>
                </td>
                <td v-else class="demurrage-text">
                  {{
                    slotProps.item.demurrageRate
                      ? `$${slotProps.item.demurrageRate}`
                      : ''
                  }}
                </td>
                <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
                <td>
                  <span
                    class="source-badge"
                    :class="`source-${determineRateSource(
                      slotProps.item.rateTableItem.originalRateTableMongoId,
                    ).toLowerCase()}`"
                  >
                    {{
                      determineRateSource(
                        slotProps.item.rateTableItem.originalRateTableMongoId,
                      )
                    }}
                  </span>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>

        <!-- Unit Rate Table -->
        <div v-if="selectedRateTypeId === JobRateType.UNIT">
          <v-data-table
            class="default-table-dark gd-dark-theme connected-rows-table"
            :headers="unitHeaders"
            :items="unitRateTableData"
            hide-actions
            :rows-per-page-items="[10, 20]"
          >
            <template v-slot:items="slotProps">
              <tr>
                <td>{{ slotProps.item.unitTypeName }}</td>
                <td>
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.unitRateObject.unitRanges.some(
                        (range) => range.unitRate > 0,
                      )
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.rate }}
                        <span
                          v-if="
                            slotProps.item.unitRateObject.unitRanges.some(
                              (range) => range.unitRate > 0,
                            )
                          "
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item.rateTableItem,
                        slotProps.item.unitRateObject.unitRanges[0].unitRate,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.rate }}
                  </span>
                </td>
                <td>{{ slotProps.item.serviceTypeName }}</td>
                <td>
                  {{ slotProps.item.unitAmountMultiplier }}
                </td>
                <td>
                  {{ slotProps.item.forklift }}
                </td>
                <td class="rate-text">
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.puFlagfallRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        {{ slotProps.item.puFlagFall }}
                        <span
                          v-if="slotProps.item.puFlagfallRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.puFlagfallRate,
                        slotProps.item.puFlagFall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else>
                    {{ slotProps.item.puFlagFall }}
                  </span>
                </td>
                <td class="rate-text">
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.doFlagfallRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        {{ slotProps.item.doFlagFall }}
                        <span
                          v-if="slotProps.item.doFlagfallRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.doFlagfallRate,
                        slotProps.item.doFlagFall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else>
                    {{ slotProps.item.doFlagFall }}
                  </span>
                </td>
                <td class="rate-text">
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.flPuFlagfallRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        {{ slotProps.item.flPuFlagFall }}
                        <span
                          v-if="slotProps.item.flPuFlagfallRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.flPuFlagfallRate,
                        slotProps.item.flPuFlagFall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else>
                    {{ slotProps.item.flPuFlagFall }}
                  </span>
                </td>
                <td class="rate-text">
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.flDoFlagfallRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        {{ slotProps.item.flDoFlagFall }}
                        <span
                          v-if="slotProps.item.flDoFlagfallRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.flDoFlagfallRate,
                        slotProps.item.flDoFlagFall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else>
                    {{ slotProps.item.flDoFlagFall }}
                  </span>
                </td>
                <td class="rate-text">
                  <v-tooltip
                    v-if="
                      showRateVariationsHighlight &&
                      slotProps.item.dgFlagfallRate > 0
                    "
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">
                        {{ slotProps.item.dgFlagFall }}
                        <span
                          v-if="slotProps.item.dgFlagfallRate > 0"
                          class="rate-variation-symbol"
                        >
                          {{ getRateVariationSymbol(slotProps.item) }}
                        </span>
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(
                        slotProps.item,
                        slotProps.item.dgFlagfallRate,
                        slotProps.item.dgFlagFall,
                      )
                    }}</span>
                  </v-tooltip>
                  <span v-else>
                    {{ slotProps.item.dgFlagFall }}
                  </span>
                </td>
                <td>
                  {{ slotProps.item.demurrageRate }}
                </td>
                <td>
                  <span
                    class="source-badge"
                    :class="`source-${determineRateSource(
                      slotProps.item.rateTableItem.originalRateTableMongoId,
                    ).toLowerCase()}`"
                  >
                    {{
                      determineRateSource(
                        slotProps.item.rateTableItem.originalRateTableMongoId,
                      )
                    }}
                  </span>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import {
  distanceHeaders,
  GroupedDataItem,
  pointToPointHeaders,
  timeHeaders,
  unitHeaders,
  zoneHeaders,
} from '@/components/admin/ClientDetails/components/client_details_rate_summary/ClientServiceRateSummaryTableData';
import {
  addPercentageTo,
  DisplayCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnChargeIncrementDescription,
  returnMinimumChargeDescription,
  returnRangeRateSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { getRateVariation } from '@/helpers/RateHelpers/RateVariationHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { returnGraceShortName } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import { rateMultipliers } from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import {
  JobRateType,
  returnSortedRateTypesList,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { returnStartAndReturnLegsLongNameById } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    clientId: string;
    searchDate?: number;
    showRateVariationsHighlight?: boolean;
    clientRates: ClientServiceRate[] | null;
    divisionRates: ClientServiceRate[] | null;
  }>(),
  {
    searchDate: Date.now(),
    showRateVariationsHighlight: true,
  },
);

const serviceRateStore = useServiceRateStore();
const serviceRateVariationsStore = useServiceRateVariationsStore();
const companyDetailsStore = useCompanyDetailsStore();

const selectedRateTypeId: Ref<JobRateType | null> = ref(
  useCompanyDetailsStore().divisionCustomConfig?.operations
    ?.defaultRateTypeId ?? null,
);

// service rates and rate variations
const serviceRate: Ref<ClientServiceRate | null> = ref(null);
const isLoadingServiceRate: Ref<boolean> = ref(false);
const rateVariations: Ref<ClientServiceRateVariations[]> = ref([]);
const isLoadingVariations: Ref<boolean> = ref(false);

const availableRateTypes: ComputedRef<ServiceTypeRates[]> = computed(() => {
  if (!serviceRate.value || !serviceRate.value.rateTableItems.length) {
    return [];
  }

  // Get unique rate type IDs present in the data
  const rateTypeIdsInData = new Set(
    serviceRate.value.rateTableItems.map((item) => item.rateTypeId),
  );

  const displayOrder =
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.rateTypeDisplayOrder;

  // Filter serviceTypeRates to only include those with data and not adhoc
  let filteredRates = returnSortedRateTypesList(displayOrder).filter(
    (rateType) => !rateType.adhoc && rateTypeIdsInData.has(rateType.rateTypeId),
  );

  return filteredRates;
});

/**
 * Computed property to check if demurrage rate variations should be applied
 * based on the division's custom configuration
 */
const shouldApplyRateVariationsToDemurrage: ComputedRef<boolean> = computed(
  () => {
    return (
      companyDetailsStore.divisionCustomConfig?.accounting
        ?.applyRateVariationsToDemurrage ?? false
    );
  },
);

/**
 * Loads service rates for the specified client
 */
async function loadServiceRate(): Promise<void> {
  if (!props.clientId) {
    return;
  }
  isLoadingServiceRate.value = true;
  try {
    const searchDate = props.searchDate || Date.now();
    const response: CurrentClientServiceRateResponse | null =
      await serviceRateStore.getMergedClientServiceRates(
        props.clientId,
        searchDate,
      );
    if (response) {
      serviceRate.value = response.clientServiceRate;
    }
  } catch (error) {
    console.error('Error loading service rates:', error);
    serviceRate.value = null;
  } finally {
    isLoadingServiceRate.value = false;
  }
}

/**
 * Loads rate variations for the specified client
 */
async function loadRateVariations(): Promise<void> {
  if (!props.clientId) {
    return;
  }
  isLoadingVariations.value = true;
  try {
    const searchDate = Date.now(); // Use current date for active variations
    const variations =
      await serviceRateVariationsStore.getServiceRateVariationsByClient(
        props.clientId,
        searchDate,
      );
    rateVariations.value = variations || [];
  } catch (error) {
    console.error('Error loading rate variations:', error);
    rateVariations.value = [];
  } finally {
    isLoadingVariations.value = false;
  }
}

/**
 * Determines the source of a rate table item based on originalRateTableMongoId
 */
function determineRateSource(
  originalRateTableMongoId: string,
): 'Custom' | 'Division' | 'Unknown' {
  if (!originalRateTableMongoId) {
    return 'Unknown';
  }

  // Check if it matches any client service rate _id
  if (props.clientRates) {
    const clientMatch = props.clientRates.find(
      (rate) => rate._id === originalRateTableMongoId,
    );
    if (clientMatch) {
      return 'Custom';
    }
  }

  // Check if it matches any division service rate _id
  if (props.divisionRates) {
    const divisionMatch = props.divisionRates.find(
      (rate) => rate._id === originalRateTableMongoId,
    );
    if (divisionMatch) {
      return 'Division';
    }
  }

  return 'Unknown';
}

/**
 * Computed property for rate table summary information
 */
const rateTableSummary = computed(() => {
  if (
    !serviceRate.value?.rateTableItems.length ||
    !props.clientRates ||
    !props.divisionRates
  ) {
    return { customTables: [], divisionTables: [] };
  }

  const customTableIds = new Set<string>();
  const divisionTableIds = new Set<string>();

  // Collect unique rate table IDs from merged service rate
  serviceRate.value.rateTableItems.forEach((item) => {
    if (item.originalRateTableMongoId) {
      const source = determineRateSource(item.originalRateTableMongoId);
      if (source === 'Custom') {
        customTableIds.add(item.originalRateTableMongoId);
      } else if (source === 'Division') {
        divisionTableIds.add(item.originalRateTableMongoId);
      }
    }
  });

  // Get table names
  const customTables = Array.from(customTableIds).map((id) => {
    const table = props.clientRates?.find((rate) => rate._id === id);
    return table?.name || 'Unknown Custom Table';
  });

  const divisionTables = Array.from(divisionTableIds).map((id) => {
    const table = props.divisionRates?.find((rate) => rate._id === id);
    return table?.name || 'Unknown Division Table';
  });

  return { customTables, divisionTables };
});

// Time Rate Items and Headers
const timeRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.TIME,
  );
});

// Distance Rate Items and Headers
const distanceRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
});

/**
 * Computed property for grouped distance rate table data
 * @returns Array of grouped distance rate table items with services as parent rows and distance configurations as child rows
 */
const distanceRateTableData = computed((): GroupedDataItem[] => {
  if (!serviceRate.value) {
    return [];
  }

  const distanceItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
  if (!distanceItems.length) {
    return [];
  }

  const groupedData: GroupedDataItem[] = [];

  distanceItems.forEach((service) => {
    const serviceName = returnServiceTypeLongNameFromId(
      service.serviceTypeId ?? 0,
    );
    const rateObj: DistanceRateType =
      service.rateTypeObject as DistanceRateType;

    const demurrageGraceAsMinutes = moment
      .duration(rateObj.demurrage.graceTimeInMilliseconds)
      .asMinutes();

    // Calculate demurrage variation if applicable
    const variationPercentage = getRateVariationPercentage(service);
    const demurrageVariationRate =
      variationPercentage !== null && shouldApplyRateVariationsToDemurrage.value
        ? `${formatRateWithVariation(service, rateObj.demurrage.rate)}%`
        : `${rateObj.demurrage.rate}`;

    // Add distance configuration child row with first range if available
    const hasRanges = rateObj.rates && rateObj.rates.length > 0;
    const firstRange = hasRanges ? rateObj.rates[0] : null;
    const firstRangeText = firstRange
      ? returnRangeRateSummary(firstRange, 0)
      : '';
    const firstRangeVariationPercentage: number =
      getRateVariationPercentage(service) ?? 0;
    const firstRangeAdjustedRate = firstRange
      ? addPercentageTo(firstRange.rate, firstRangeVariationPercentage)
      : 0;
    const firstRangeDisplay = firstRange
      ? `$${DisplayCurrencyValue(firstRangeAdjustedRate)}/km`
      : '';

    // Main distance row
    groupedData.push({
      isServiceHeader: false,
      isDistanceRow: true,
      serviceTypeName: serviceName,
      serviceTypeId: service.serviceTypeId ?? null,
      baseFreight: DisplayCurrencyValue(firstRangeAdjustedRate),
      calculation: `${returnReadableChargeBasisName(
        rateObj.chargeBasis,
      )} (${returnReadableRateBracketTypeName(rateObj.rateBracketType)})`,
      increment: returnChargeIncrementDescription(rateObj),
      legs: `${
        returnStartAndReturnLegsLongNameById(rateObj.firstLegTypeId) || 'N/A'
      } / ${
        returnStartAndReturnLegsLongNameById(rateObj.lastLegTypeId) || 'N/A'
      }`,
      minimumCharge: returnMinimumChargeDescription(rateObj),
      demurrageGrace: !demurrageGraceAsMinutes
        ? 'None'
        : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
      demurrageRate: `${demurrageVariationRate} - ${demurrageGraceAsMinutes} min grace`,
      demurrage: rateObj.demurrage.rate,
      rangeText: firstRangeText,
      range: hasRanges && rateObj.rates.length === 1 ? firstRangeDisplay : '',
      rateTableItem: service,
      rateTypeObject: rateObj,
      rangeRate: hasRanges && rateObj.rates.length === 1 ? firstRange : null,
    });

    //  Additional child range rows
    if (rateObj.rates && rateObj.rates.length > 1) {
      const variationPercentage: number =
        getRateVariationPercentage(service) ?? 0;

      rateObj.rates.forEach((rangeRate, index) => {
        const rangeText = returnRangeRateSummary(rangeRate, index);
        const adjustedRate = addPercentageTo(
          rangeRate.rate,
          variationPercentage,
        );
        const rangeDisplay = `$${DisplayCurrencyValue(adjustedRate)}/km`;

        groupedData.push({
          isServiceHeader: false,
          isDistanceRow: true,
          isRangeRow: true,
          rangeText,
          range: rangeDisplay,
          rateTableItem: service,
          rateTypeObject: rateObj,
          rangeRate,
        });
      });
    }
  });

  return groupedData;
});

/**
 * Computed property for grouped zone rate table data
 * @returns Array of grouped zone rate table items with services as parent rows and zones as child rows
 */
const zoneRateTableData = computed((): GroupedDataItem[] => {
  if (!serviceRate.value) {
    return [];
  }

  const zoneRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.ZONE,
  );
  if (!zoneRateItems.length) {
    return [];
  }

  const groupedData: GroupedDataItem[] = [];

  zoneRateItems.forEach((service) => {
    const zoneRates: ZoneRateType[] = service.rateTypeObject as ZoneRateType[];
    const serviceName = returnServiceTypeLongNameFromId(
      service.serviceTypeId ?? 0,
    );

    // Add service header row (no filler fields)
    groupedData.push({
      isServiceHeader: true,
      serviceTypeName: serviceName,
      serviceTypeId: service.serviceTypeId ?? null,
      rateTableItem: service,
    });

    // Add zone child rows
    zoneRates.forEach((zone: ZoneRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => zone.appliedFuelSurchargeId === x.id,
      );

      const demurrageGraceAsMinutes = moment
        .duration(zone.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      // Calculate demurrage variation if applicable
      const variationPercentage = getRateVariationPercentage(service);
      const demurrageVariationRate =
        variationPercentage !== null &&
        shouldApplyRateVariationsToDemurrage.value
          ? `${formatRateWithVariation(service, zone.demurrage.rate)}%`
          : `${zone.demurrage.rate}`;

      groupedData.push({
        isServiceHeader: false,
        isRangeRow: true,
        serviceTypeName: zone.zoneName,
        serviceTypeId: service.serviceTypeId ?? null,
        index,
        rate: zone.rate,
        zoneRate: formatRateWithVariation(service, zone.rate),
        additionalPickUpFlagFall: zone.additionalPickUpFlagFall,
        pickupFlagfall: formatRateWithVariation(
          service,
          zone.additionalPickUpFlagFall,
        ),
        additionalDropOffFlagFall: zone.additionalDropOffFlagFall,
        dropoffFlagfall: formatRateWithVariation(
          service,
          zone.additionalDropOffFlagFall,
        ),
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `${demurrageVariationRate} - ${demurrageGraceAsMinutes} min grace`,
        demurrage: zone.demurrage.rate,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        calculation: `${zone.percentage}%`,
        demurrageFuelSurchargeApplies: zone.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
      });
    });
  });

  return groupedData;
});

/**
 * Computed property for point-to-point rate table data
 * @returns Array of point-to-point rate table items
 */
const pointToPointRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const pointToPointRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.POINT_TO_POINT,
  );
  if (!pointToPointRateItems.length) {
    return [];
  }

  return pointToPointRateItems.flatMap((service) => {
    const rates: PointToPointRateType[] =
      service.rateTypeObject as PointToPointRateType[];

    return rates.map((rateType: PointToPointRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => rateType.appliedFuelSurchargeId === x.id,
      );
      const demurrageGraceAsMinutes = moment
        .duration(rateType.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      // Calculate demurrage variation if applicable
      const variationPercentage = getRateVariationPercentage(service);
      const demurrageVariationRate =
        variationPercentage !== null &&
        shouldApplyRateVariationsToDemurrage.value
          ? `${formatRateWithVariation(service, rateType.demurrage.rate)}%`
          : `${rateType.demurrage.rate}`;
      return {
        index,
        serviceTypeName: returnServiceTypeLongNameFromId(
          service.serviceTypeId ?? 0,
        ),
        serviceTypeId: service.serviceTypeId ?? null,
        fromAddress: rateType.fromAddressReference,
        toAddress: rateType.toAddressReference,
        rate: formatRateWithVariation(service, rateType.rate),
        baseRate: rateType.rate,
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `${demurrageVariationRate} - ${demurrageGraceAsMinutes} min grace`,
        demurrage: rateType.demurrage.rate,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${rateType.percentage}%`,
        demurrageFuelSurchargeApplies: rateType.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
      };
    });
  });
});

/**
 * Computed property for unit rate table data
 * @returns Array of unit rate table items
 */
const unitRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const unitRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.UNIT,
  );
  if (!unitRateItems.length) {
    return [];
  }

  return unitRateItems.flatMap((service) => {
    const unitRates: UnitRate[] = service.rateTypeObject as UnitRate[];

    return unitRates.map((unit: UnitRate, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => unit.appliedFuelSurchargeId === x.id,
      );

      // Get the variation percentage from the original service
      const variationPercentage: number =
        getRateVariationPercentage(service) ?? 0;

      const adjustedRates = unit.unitRanges.map((range) => {
        const adjustedRate = addPercentageTo(
          range.unitRate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(adjustedRate)}`;
      });

      function calculateVariation(rate: number) {
        const adjustedRate = addPercentageTo(rate, variationPercentage);

        return `$${DisplayCurrencyValue(adjustedRate)}`;
      }

      const demurrageGraceAsMinutes = moment
        .duration(unit.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      return {
        index,
        serviceTypeName: returnServiceTypeLongNameFromId(
          service.serviceTypeId ?? 0,
        ),
        serviceTypeId: service.serviceTypeId ?? null,
        unitTypeName: unit.unitTypeName,
        zoneName: unit.zoneName,
        rate: adjustedRates.join(', '),
        puFlagFall: calculateVariation(unit.pickUpFlagFallHand),
        puFlagfallRate: unit.pickUpFlagFallHand,
        doFlagFall: calculateVariation(unit.dropOffFlagFallHand),
        doFlagfallRate: unit.dropOffFlagFallHand,
        flPuFlagFall: calculateVariation(unit.pickUpFlagFallForkLift),
        flPuFlagfallRate: unit.pickUpFlagFallForkLift,
        flDoFlagFall: calculateVariation(unit.dropOffFlagFallForkLift),
        flDoFlagfallRate: unit.dropOffFlagFallForkLift,
        dgFlagFall: calculateVariation(unit.dangerousGoodsFlagFall),
        dgFlagfallRate: unit.dangerousGoodsFlagFall,
        forklift: unit.forkLiftRequired ? 'Yes' : 'NO',
        unitAmountMultiplier: unit.unitRanges
          .map((range) => range.unitAmountMultiplier)
          .join(', '),
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: ${demurrageGraceAsMinutes} min grace`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        fleetPercentage: `${unit.fleetAssetPercentage}%`,
        demurrageFuelSurchargeApplies: unit.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
        unitRateObject: unit,
      };
    });
  });
});

/**
 * Gets the rate variation percentage for a specific rate table item
 * @param item - The rate table item to check for variations
 * @returns The variation percentage or null if no variation applies
 */
function getRateVariationPercentage(item: RateTableItems): number | null {
  if (!rateVariations.value.length) {
    return null;
  }

  if (item.serviceTypeId) {
    const variation = getRateVariation({
      serviceTypeId: item.serviceTypeId,
      rateTypeId: item.rateTypeId,
      rateVariations: rateVariations.value,
    });

    if (variation && variation.clientAdjustmentPercentage) {
      return variation.clientAdjustmentPercentage;
    }

    if (variation && variation.fleetAssetAdjustmentPercentage) {
      return variation.fleetAssetAdjustmentPercentage;
    }
  }

  return null;
}

/**
 * Gets the color class for rate variation display
 * @param item - The rate table item to check for variations
 * @returns Color class name for styling
 */
function getRateVariationSymbol(item: RateTableItems): string {
  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null || !props.showRateVariationsHighlight) {
    return '';
  }

  if (variationPercentage > 0) {
    return '*';
  } else {
    return '';
  }
}

/**
 * Formats fuel surcharge display text
 * @param fuelSurchargeId - The fuel surcharge ID
 * @returns Formatted fuel surcharge text
 */
function formatFuelSurcharge(fuelSurchargeId: number): string {
  const fuelSurcharge = applicableFuelSurcharges.find(
    (f) => f.id === fuelSurchargeId,
  );
  return fuelSurcharge?.shortName || '-';
}

/**
 * Formats time rate with variation applied and appropriate unit
 * @param item - The rate table item containing time rate data
 * @returns Formatted time rate string with currency and unit
 */
function formatRateWithVariation(item: RateTableItems, rate: number): string {
  const rateObj = item.rateTypeObject;
  const multiplier = rateMultipliers.find(
    (m) => m.id === (rateObj as TimeRateType).rateMultiplier,
  );
  const unit = multiplier?.shortName || '';

  const variationPercentage: number = getRateVariationPercentage(item) ?? 0;
  const adjustedRate = addPercentageTo(rate, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}${unit ? '/' : ''}${unit}`;
}

/**
 * Gets consolidated tooltip text for all rate types showing variation calculations
 * @param item - The table item (can be any rate table item type)
 * @param rate - base rate
 * @param adjustedRate - applied rate to format tooltip for
 * @returns Tooltip text showing base rate and variation calculation
 */
function getRateTooltip(
  item: RateTableItems,
  rate: number,
  adjustedRate?: string,
): string {
  const variationPercentage = getRateVariationPercentage(item);
  const sign =
    variationPercentage !== null && variationPercentage >= 0 ? '+' : '';

  if (variationPercentage === null) {
    return `Base pickup flagfall: $${DisplayCurrencyValue(rate)}`;
  }

  if (adjustedRate) {
    return `$${rate} ${sign} ${variationPercentage}% = ${adjustedRate}`;
  } else {
    const adjustedRate = addPercentageTo(rate, variationPercentage);
    return `$${DisplayCurrencyValue(
      rate,
    )} ${sign} ${variationPercentage}% = $${DisplayCurrencyValue(
      adjustedRate,
    )}`;
  }
}

/**
 * Component mounted lifecycle hook
 * Loads service rates and rate variations, then auto-selects first available rate type
 */
onMounted(async () => {
  try {
    await Promise.all([loadServiceRate(), loadRateVariations()]);

    // Auto-select the first available rate type if the current selection is not available
    if (availableRateTypes.value.length > 0) {
      const currentRateTypeAvailable = availableRateTypes.value.some(
        (rateType) => rateType.rateTypeId === selectedRateTypeId.value,
      );

      if (!currentRateTypeAvailable) {
        selectedRateTypeId.value = availableRateTypes.value[0].rateTypeId;
      }
    }
  } catch (error) {
    console.error('Error during component initialization:', error);
  }
});
</script>

<style scoped lang="scss">
.service-rate-table-read-only {
  .rate-text {
    font-weight: bold;
    cursor: pointer;
    margin-left: 12px;
  }
  .demurrage-text {
    font-weight: bold;
    cursor: pointer;
  }
}

.text-muted {
  color: var(--light-text-color);
}

.zone-child-row {
  border-top: none !important;
  .zone-child-text {
    padding-left: 20px;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
}

.range-row {
  border-top: none !important;
  .distance-child-text {
    padding-left: 40px;
    font-size: 12px;
    color: var(--text-color-muted);
    font-style: italic;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
}

.connected-rows-table {
  padding: 24px 12px;
  border: none !important;
}

.service-name {
  color: var(--bg-light);
  font-weight: 500;
}

.rate-variation-symbol {
  color: $warning;
}

.rate-table-summary-card {
  background: var(--bg-dark-secondary);
  // margin-left: 12px;

  .custom-rates-label {
    // margin-left: 12px;
    color: $primary-light;
  }

  .division-rates-label {
    // margin-left: 12px;
    color: $accent;
  }
}

.source-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 600;
  letter-spacing: 0.5px;

  &.source-custom {
    // background-color: rgba(76, 175, 80, 0.2);
    // color: $success;
    color: $bg-light-yellow;
    border: 1px solid rgba(175, 116, 76, 0.3);
  }

  &.source-division {
    // background-color: rgba(33, 150, 243, 0.2);
    // color: $info;
    color: $bg-light-blue;
    border: 1px solid rgba(33, 150, 243, 0.3);
  }

  &.source-unknown {
    background-color: rgba(158, 158, 158, 0.2);
    color: #9e9e9e;
    border: 1px solid rgba(158, 158, 158, 0.3);
  }
}
</style>
