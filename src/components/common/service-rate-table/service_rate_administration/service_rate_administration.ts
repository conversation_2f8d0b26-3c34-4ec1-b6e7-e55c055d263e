import ServiceRateTableCustom from '@/components/admin/FleetAsset/fleet-asset-service-rate-table/service-rate-table-custom/service_rate_table_custom.vue';
import ActiveRatesSummary from '@/components/common/service-rate-table/active_rates_summary/index.vue';
import ServiceRateDateRangeEdit from '@/components/common/service-rate-table/service-rate-date-range-edit/index.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnLatestKnownClientServiceRate,
  returnLatestKnownFleetAssetServiceRate,
  setDefaultClientServiceRate,
  setDefaultFleetAssetServiceRate,
} from '@/helpers/RateHelpers/RateHelpers';
import {
  addOrRemoveRateTableItems,
  getServiceTypeIdsForRateTypeId,
  returnCleanRateTableItem,
} from '@/helpers/RateHelpers/ServiceRateHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import serviceSettings, {
  ServiceSettings,
} from '@/interface-models/Generic/ServiceTypes/ServiceSettings';
import {
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { Component, Prop, Vue } from 'vue-property-decorator';

enum ViewType {
  EDIT = 'EDIT',
  NEW = 'NEW',
  VIEW = 'VIEW',
  TABLE = 'TABLE',
}
@Component({
  components: {
    ServiceRateTableCustom,
    ServiceRateDateRangeEdit,
    ActiveRatesSummary,
  },
})
export default class ServiceRateAdministration extends Vue {
  @Prop({ default: RateEntityType.FLEET_ASSET })
  public serviceRateType: RateEntityType;
  @Prop({ default: false }) public isEdited: boolean;
  @Prop() public hasServiceRateTable: boolean;
  // Fleet Asset
  @Prop() public fleetAssetDetails: FleetAsset;
  @Prop() public currentActiveFleetServiceRate: FleetAssetServiceRate;
  @Prop() public allFleetAssetServiceRates: FleetAssetServiceRate[];
  // Client
  @Prop() public clientDetails: ClientDetails;
  @Prop() public currentActiveClientServiceRate: ClientServiceRate;
  @Prop() public currentDivisionServiceRate: ClientServiceRate;
  @Prop() public allClientServiceRates: ClientServiceRate[];
  // Fuel Surcharge
  @Prop() public allFuelSurchargeRates: FuelSurchargeRate[];
  @Prop() public activeFuelSurchargeId: string | null;

  public serviceRateStore = useServiceRateStore();

  public viewType: ViewType = ViewType.TABLE;

  public isRequestingTableData: boolean = false;

  public fleetAssetServiceRate: FleetAssetServiceRate | null = null;
  public clientServiceRate: ClientServiceRate | null = null;

  public serviceSettings: ServiceSettings[] = serviceSettings;
  public serviceTypeRates: ServiceTypeRates[] = serviceTypeRates.filter(
    (s) => s.rateTypeId !== 6 && s.rateTypeId !== 5,
  );

  public serviceTypesWithTimeRate: number[] = [];
  public serviceTypesWithZoneRate: number[] = [];
  public serviceTypesWithPointToPoint: number[] = [];
  public serviceTypesWithDistanceRate: number[] = [];

  get fuelSurchargeRate(): FuelSurchargeRate | null {
    const activeId = this.activeFuelSurchargeId;
    if (activeId === null) {
      return null;
    }
    const foundActive = this.allFuelSurchargeRates.find(
      (r) => r.id === this.activeFuelSurchargeId,
    );
    return foundActive ? foundActive : new FuelSurchargeRate();
  }

  get validServiceTypeList() {
    return useCompanyDetailsStore().getServiceTypesList.filter(
      (serviceType: ServiceTypes) => serviceType.serviceTypeId !== 4,
    );
  }

  get unitRateAppliesController() {
    if (
      this.serviceRateType === RateEntityType.CLIENT &&
      this.clientServiceRate
    ) {
      const foundUnitRate = this.clientServiceRate.rateTableItems.find(
        (r) => r.rateTypeId === 5,
      );
      return foundUnitRate !== undefined;
    } else if (
      this.serviceRateType === RateEntityType.FLEET_ASSET &&
      this.fleetAssetServiceRate
    ) {
      const foundUnitRate = this.fleetAssetServiceRate.rateTableItems.find(
        (r) => r.rateTypeId === 5,
      );
      return foundUnitRate !== undefined;
    }

    return false;
  }
  set unitRateAppliesController(value: boolean) {
    if (value) {
      this.addOrRemoveRateTableItems(5, [4]);
    } else {
      this.addOrRemoveRateTableItems(5, []);
    }
  }

  // Modelled to the autocomplete controlling applied TIME rates. Calls method
  // to update rateTableItems on change
  get timeAppliedController(): number[] {
    return this.serviceTypesWithTimeRate;
  }
  set timeAppliedController(value: number[]) {
    this.serviceTypesWithTimeRate = value;
    this.addOrRemoveRateTableItems(1, value);
  }
  // Modelled to the autocomplete controlling applied ZONE rates. Calls method
  // to update rateTableItems on change
  get zoneAppliedController(): number[] {
    return this.serviceTypesWithZoneRate;
  }
  set zoneAppliedController(value: number[]) {
    this.serviceTypesWithZoneRate = value;
    this.addOrRemoveRateTableItems(2, value);
  }
  // Modelled to the autocomplete controlling applied P2P rates. Calls method
  // to update rateTableItems on change
  get p2pAppliedController(): number[] {
    return this.serviceTypesWithPointToPoint;
  }
  set p2pAppliedController(value: number[]) {
    this.serviceTypesWithPointToPoint = value;
    this.addOrRemoveRateTableItems(4, value);
  }
  // Modelled to the autocomplete controlling applied DISTANCE rates. Calls method
  // to update rateTableItems on change
  get distanceAppliedController(): number[] {
    return this.serviceTypesWithDistanceRate;
  }
  set distanceAppliedController(value: number[]) {
    this.serviceTypesWithDistanceRate = value;
    this.addOrRemoveRateTableItems(3, value);
  }

  // Handle emit from DateRangeEdit component when the request is sent to
  // request rates
  public setIsRequestingTableData(value: boolean) {
    this.isRequestingTableData = value;
  }
  // Handle emit from DateRangeEdit component to send request for service rates
  public async getServiceRateByTableId(payload: [string, string]) {
    this.setIsRequestingTableData(true);

    // Make appropriate request and await response
    let serviceRate: FleetAssetServiceRate | ClientServiceRate | null = null;
    if (this.serviceRateType === RateEntityType.FLEET_ASSET) {
      serviceRate =
        await this.serviceRateStore.getFleetAssetServiceRateByTableId(
          payload[0],
          payload[1],
        );
      // Handle response
      if (serviceRate) {
        this.handleServiceRateByTableIdResponse({
          type: this.serviceRateType,
          serviceRate,
        });
      }
    } else if (this.serviceRateType === RateEntityType.CLIENT) {
      serviceRate = await this.serviceRateStore.getClientServiceRatesByTableId(
        payload[0],
        payload[1],
      );
      // Handle response
      if (serviceRate) {
        this.handleServiceRateByTableIdResponse({
          type: this.serviceRateType,
          serviceRate,
        });
      }
    }
    // Show notification if response is null
    if (!serviceRate) {
      showNotification(GENERIC_ERROR_MESSAGE);
    }

    this.setIsRequestingTableData(false);
  }

  /**
   * Handles the service rate details based on the type of the service rate.
   *
   * @param serviceRateDetails - The service rate details. This can be either a
   * client service rate or a fleet asset service rate. The type of the service
   * rate is determined by the `type` property of the `serviceRateDetails`
   * object.
   */
  private handleServiceRateByTableIdResponse(
    serviceRateDetails:
      | { type: RateEntityType.CLIENT; serviceRate: ClientServiceRate }
      | {
          type: RateEntityType.FLEET_ASSET;
          serviceRate: FleetAssetServiceRate;
        },
  ) {
    // If the service rate type is a fleet asset, set the fleet asset service rate
    if (serviceRateDetails.type === RateEntityType.FLEET_ASSET) {
      const rate = serviceRateDetails.serviceRate;
      this.fleetAssetServiceRate = rate;
    }
    // If the service rate type is a client, set the client service rate
    else if (this.serviceRateType === RateEntityType.CLIENT) {
      const rate = serviceRateDetails.serviceRate;
      this.clientServiceRate = rate;
    }
    // Set the settings
    this.setSettings();

    // Set view type and update component state
    if (this.viewType === ViewType.TABLE) {
      this.viewType = ViewType.VIEW;
    } else if (this.viewType === ViewType.NEW) {
      this.setStateForNewServiceRate();
    }
  }

  public setNewServiceRate() {
    this.viewType = ViewType.NEW;
    if (this.serviceRateType === RateEntityType.FLEET_ASSET) {
      this.fleetAssetServiceRate = new FleetAssetServiceRate();
    } else {
      this.clientServiceRate = new ClientServiceRate();
    }
    this.setStateForNewServiceRate();
  }
  public cancelNewServiceRate() {
    this.viewType = ViewType.TABLE;
    this.fleetAssetServiceRate = null;
  }
  public setViewingServiceRate(value: boolean) {
    // If just viewing
    if (value) {
      this.viewType = ViewType.VIEW;
    } else {
      this.viewType = ViewType.TABLE;
      this.fleetAssetServiceRate = null;
    }
  }
  public setEditServiceRate(value: boolean = true) {
    if (value) {
      this.viewType = ViewType.EDIT;
    } else {
      this.viewType = ViewType.TABLE;
      this.fleetAssetServiceRate = null;
    }
  }
  // Handle 'close' click event on chip in v-autocomplete in html. Remove the
  // selected serviceTypeId from the list specified in listToSplice
  public removeServiceTypeIdFromList(
    listToSplice: string,
    serviceTypeId: number,
  ) {
    switch (listToSplice) {
      case 'TIME':
        this.timeAppliedController = this.timeAppliedController.filter(
          (i) => i !== serviceTypeId,
        );
        break;
      case 'ZONE':
        this.zoneAppliedController = this.zoneAppliedController.filter(
          (i) => i !== serviceTypeId,
        );
        break;
      case 'P2P':
        this.p2pAppliedController = this.p2pAppliedController.filter(
          (i) => i !== serviceTypeId,
        );
        break;
      case 'DISTANCE':
        this.distanceAppliedController = this.distanceAppliedController.filter(
          (i) => i !== serviceTypeId,
        );
        break;
    }
  }

  // Update the rateTableItems with the provided rateTypeId, to contain
  // rateTableItems with the supplied list of serviceTypeIds
  // (updatedAppliedServiceTypes). Removes items that are not in the incoming
  // list, and adds any from incoming list that are not in the current list
  public addOrRemoveRateTableItems(
    rateTypeId: number,
    updatedAppliedServiceTypes: number[],
  ) {
    if (
      this.serviceRateType === RateEntityType.FLEET_ASSET &&
      !!this.fleetAssetServiceRate
    ) {
      addOrRemoveRateTableItems({
        type: RateEntityType.FLEET_ASSET,
        serviceRates: this.fleetAssetServiceRate,
        rateTypeId,
        updatedAppliedServiceTypes,
      });
    }
    if (
      this.serviceRateType === RateEntityType.CLIENT &&
      !!this.clientServiceRate
    ) {
      addOrRemoveRateTableItems({
        type: RateEntityType.CLIENT,
        serviceRates: this.clientServiceRate,
        rateTypeId,
        updatedAppliedServiceTypes,
      });
    }
  }

  public handleChange(
    serviceTypeId: number,
    rateTypeId: number,
    selectionTypeId: number,
  ) {
    if (!this.fleetAssetServiceRate) {
      return;
    }

    const fsr = this.fleetAssetServiceRate;
    if (selectionTypeId === 2) {
      // We are applying CUSTOM RATES
      // Find existing rate for this service type id / rate type combination
      const foundRate = fsr.rateTableItems.find(
        (r) => r.rateTypeId === rateTypeId && r.serviceTypeId === serviceTypeId,
      );
      if (foundRate) {
        // Rate already exists - do nothing
      } else {
        // Rate does not exist - we should add it
        const rateTableItem = returnCleanRateTableItem(
          serviceTypeId,
          rateTypeId,
        );
        if (rateTableItem) {
          fsr.rateTableItems.push(rateTableItem);
        }
      }
    } else {
      // NO RATES REQUIRED for this selection
      const foundRateIndex = fsr.rateTableItems.findIndex(
        (r) => r.rateTypeId === rateTypeId && r.serviceTypeId === serviceTypeId,
      );
      // If the rate exists we should remove it
      if (foundRateIndex !== -1) {
        // Found the rate, we should remove this index
        fsr.rateTableItems.splice(foundRateIndex, 1);
      } else {
        // If not found, do nothing
      }
    }
  }

  public setSettings() {
    if (
      this.serviceRateType === RateEntityType.FLEET_ASSET &&
      (!this.fleetAssetServiceRate ||
        !this.fleetAssetServiceRate.rateTableItems)
    ) {
      return;
    }
    if (
      this.serviceRateType === RateEntityType.CLIENT &&
      (!this.clientServiceRate || !this.clientServiceRate.rateTableItems)
    ) {
      return;
    }

    const rateTableItems =
      this.serviceRateType === RateEntityType.FLEET_ASSET
        ? this.fleetAssetServiceRate!.rateTableItems
        : this.clientServiceRate!.rateTableItems;
    // List of service type ids with rateTypeId of 1 for TIME
    const timeRateApplied = getServiceTypeIdsForRateTypeId(1, rateTableItems);
    // List of service type ids with rateTypeId of 2 for ZONE
    const zoneRateApplied = getServiceTypeIdsForRateTypeId(2, rateTableItems);
    // List of service type ids with rateTypeId of 4 for P2P
    const p2pRateApplied = getServiceTypeIdsForRateTypeId(4, rateTableItems);
    // List of service type ids with rateTypeId of 3 for DISTANCE
    const distanceRateApplied = getServiceTypeIdsForRateTypeId(
      3,
      rateTableItems,
    );

    this.serviceTypesWithTimeRate = timeRateApplied;
    this.serviceTypesWithZoneRate = zoneRateApplied;
    this.serviceTypesWithPointToPoint = p2pRateApplied;
    this.serviceTypesWithDistanceRate = distanceRateApplied;
  }

  // Using either the latest known service rate, or one that is supplied as an
  // optional parameter, initialise the fleetAssetServiceRate object with
  // provided values
  public setStateForNewServiceRate() {
    if (this.serviceRateType === RateEntityType.FLEET_ASSET) {
      const latestKnownServiceRate = returnLatestKnownFleetAssetServiceRate(
        this.allFleetAssetServiceRates,
      );

      setDefaultFleetAssetServiceRate(
        this.fleetAssetServiceRate,
        latestKnownServiceRate,
        this.fleetAssetDetails.fleetAssetId,
      );
    } else if (this.serviceRateType === RateEntityType.CLIENT) {
      const latestKnownServiceRate = returnLatestKnownClientServiceRate(
        this.allClientServiceRates,
      );
      if (this.clientDetails.clientId) {
        setDefaultClientServiceRate(
          this.clientServiceRate,
          latestKnownServiceRate,
          this.clientDetails.clientId!,
        );
      }
    }

    this.setSettings();
  }

  public savedRateTable(): void {
    this.viewType = ViewType.TABLE;
    this.fleetAssetServiceRate = null;
    this.$emit('refreshServiceRateList');
  }
  // Call method in child to initiate save process
  public saveServiceRateInChild(): void {
    if (this.serviceRateType === RateEntityType.CLIENT) {
      const foundServiceRateComponent = this.$refs
        .clientServiceRateTableCustom as any;

      if (foundServiceRateComponent) {
        foundServiceRateComponent.saveServiceRateData();
      }
    } else if (this.serviceRateType === RateEntityType.FLEET_ASSET) {
      const foundServiceRateComponent = this.$refs
        .fleetAssetServiceRateTableCustom as any;

      if (foundServiceRateComponent) {
        foundServiceRateComponent.saveServiceRateData();
      }
    }
  }

  get settingsInputsDisabled() {
    return this.viewType !== ViewType.EDIT && this.viewType !== ViewType.NEW;
  }

  /**
   * computed list of this clients common addresses.
   * @return {ClientCommonAddress[] } list of client common addresses from ClientDetailsModule
   */
  get clientCommonAddresses(): ClientCommonAddress[] {
    return useClientDetailsStore().clientCommonAddresses;
  }

  /**
   * fetch client site contacts
   * @return {void} void
   */
  public async getClientsCommonAddresses(): Promise<void> {
    if (!this.clientDetails || !this.clientDetails.clientId) {
      return;
    }
    // fetch client site contacts
    const commonAddresses: ClientCommonAddress[] | null =
      await useClientDetailsStore().getClientCommonAddressesByClientId(
        this.clientDetails.clientId,
      );
    if (!commonAddresses) {
      console.error(
        "Something went wrong when fetching client's common addresses.",
      );
      return;
    }
  }

  public mounted() {
    this.getClientsCommonAddresses();
  }
}
