<section class="fleet-asset-service-rate-index">
  <v-flex md12>
    <v-layout>
      <v-flex
        :md5="['NEW', 'VIEW', 'EDIT'].includes(viewType)"
        :md12="!['NEW', 'VIEW', 'EDIT'].includes(viewType)"
      >
        <v-layout>
          <ServiceRateDateRangeEdit
            v-if="serviceRateType === 'FLEET_ASSET'"
            :key="viewType"
            :fleetAssetId="fleetAssetDetails.fleetAssetId"
            :isFleetAsset="true"
            :isClient="false"
            :isNew="viewType === 'NEW'"
            :isEdited="viewType === 'EDIT' || viewType === 'NEW'"
            :isViewingServiceRate="viewType === 'VIEW'"
            :newServiceRateSettings="viewType === 'NEW'"
            :fleetAssetHasActiveRates="currentActiveFleetServiceRate !== null"
            :allClientServiceRates="allFleetAssetServiceRates"
            :allServiceRates="allFleetAssetServiceRates"
            :clientFleetId="fleetAssetDetails.fleetAssetId"
            :hasServiceRateTable="hasServiceRateTable"
            :serviceRate="fleetAssetServiceRate"
            :allowEditOnDateRange="false"
            :currentActiveServiceRate="currentActiveFleetServiceRate"
            :clientAssetName="fleetAssetDetails.csrAssignedId"
            :singlePageLayout="true"
            :handleResponsesLocally="true"
            @editSettings="setEditServiceRate"
            @setNewClientServiceRate="setNewServiceRate"
            @cancelNewClientServiceRate="cancelNewServiceRate"
            @setViewingServiceRate="setViewingServiceRate"
            @setIsRequestingTableData="setIsRequestingTableData"
            @getServiceRateByTableId="getServiceRateByTableId"
            @copyFromIncomingServiceRate="setStateForNewServiceRate"
          >
            <ActiveRatesSummary
              slot="active-rates-summary"
              serviceRateType="FLEET_ASSET"
              :fleetAssetDetails="fleetAssetDetails"
              :currentServiceRate="currentActiveFleetServiceRate"
              :currentFuelSurcharge="fuelSurchargeRate"
            ></ActiveRatesSummary>
          </ServiceRateDateRangeEdit>
          <ServiceRateDateRangeEdit
            v-if="serviceRateType === 'CLIENT'"
            :key="viewType"
            :isClient="true"
            :isNew="viewType === 'NEW'"
            :isEdited="viewType === 'EDIT' || viewType === 'NEW'"
            :isViewingServiceRate="viewType === 'VIEW'"
            :newServiceRateSettings="viewType === 'NEW'"
            :fleetAssetHasActiveRates="currentActiveClientServiceRate !== null"
            :allClientServiceRates="allClientServiceRates"
            :allServiceRates="allClientServiceRates"
            :clientFleetId="clientDetails.clientId"
            :hasServiceRateTable="hasServiceRateTable"
            :serviceRate="clientServiceRate"
            :allowEditOnDateRange="false"
            :currentActiveServiceRate="currentActiveClientServiceRate"
            :clientAssetName="clientDetails.clientName"
            :singlePageLayout="true"
            :handleResponsesLocally="true"
            @editSettings="setEditServiceRate"
            @setNewClientServiceRate="setNewServiceRate"
            @cancelNewClientServiceRate="cancelNewServiceRate"
            @setViewingServiceRate="setViewingServiceRate"
            @setIsRequestingTableData="setIsRequestingTableData"
            @getServiceRateByTableId="getServiceRateByTableId"
            @copyFromIncomingServiceRate="setStateForNewServiceRate"
          >
            <ActiveRatesSummary
              slot="active-rates-summary"
              serviceRateType="CLIENT"
              :clientDetails="clientDetails"
              :currentServiceRate="currentActiveClientServiceRate"
              :currentFuelSurcharge="fuelSurchargeRate"
            ></ActiveRatesSummary>
          </ServiceRateDateRangeEdit>
        </v-layout>
      </v-flex>
      <v-flex
        md7
        v-if="(fleetAssetServiceRate !== null || clientServiceRate !== null) && ['NEW', 'VIEW', 'EDIT'].includes(viewType)"
      >
        <v-layout justify-end px-3 pb-2>
          <v-btn
            depressed
            color="primary"
            @click="saveServiceRateInChild"
            :disabled="viewType === 'VIEW'"
            class="add-asset-btn"
          >
            <v-icon small class="mr-2">save</v-icon>
            {{ `Save ${viewType === 'EDIT' || viewType === 'VIEW' ? 'Changes' :
            viewType === 'NEW' ? 'New Rates' : ''}` }}
          </v-btn>
        </v-layout>
        <v-layout row wrap px-3>
          <v-flex md12 pb-3
            ><v-layout align-center pl-4>
              <h3 class="subheader--light pr-3 pt-1">Select Applied Rates</h3>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <!-- <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0"
                    >Current Applied Rates:</h6
                  >
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-autocomplete
                  class="v-solo-custom"
                  solo
                  flat
                  multiple
                  :disabled="settingsInputsDisabled"
                  v-model="viewingSettingsRows"
                  :items="validServiceTypeList"
                  item-text="optionSelectName"
                  color="orange"
                  label="Service Type"
                  item-value="serviceTypeId"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      :color="viewType === 'EDIT' || viewType === 'NEW' ? 'blue' : 'grey darken-3'"
                    >
                      <span>{{ item.optionSelectName }}</h5>
                    </v-chip>
                  </template>
                </v-autocomplete>
              </v-flex>
            </v-layout>
          </v-flex> -->
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Time Rate Applied:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-autocomplete
                  class="v-solo-custom"
                  solo
                  flat
                  multiple
                  :disabled="settingsInputsDisabled"
                  v-model="timeAppliedController"
                  :items="validServiceTypeList"
                  item-text="optionSelectName"
                  color="orange"
                  label="Service Type"
                  item-value="serviceTypeId"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      :color="viewType === 'EDIT' || viewType === 'NEW' ? 'green' : 'grey darken-3'"
                      close
                      @input="removeServiceTypeIdFromList('TIME', item.serviceTypeId)"
                    >
                      <span>{{ item.optionSelectName }}</span>
                    </v-chip>
                    <!-- <span v-if="index === 1" class="grey--text caption"
                  >(+{{ viewingSettingsRows.length - 1 }} others)</span
                > -->
                  </template>
                </v-autocomplete>
              </v-flex></v-layout
            >
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Zone Rate Applied:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-autocomplete
                  class="v-solo-custom"
                  solo
                  flat
                  multiple
                  :disabled="settingsInputsDisabled"
                  v-model="zoneAppliedController"
                  :items="validServiceTypeList"
                  item-text="optionSelectName"
                  color="orange"
                  label="Service Type"
                  item-value="serviceTypeId"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      :color="viewType === 'EDIT' || viewType === 'NEW' ? 'green' : 'grey darken-3'"
                      :close="viewType === 'EDIT' || viewType === 'NEW'"
                      @input="removeServiceTypeIdFromList('ZONE', item.serviceTypeId)"
                    >
                      <span>{{ item.optionSelectName }}</span>
                    </v-chip>
                    <!-- <span v-if="index === 1" class="grey--text caption"
                  >(+{{ viewingSettingsRows.length - 1 }} others)</span
                > -->
                  </template>
                </v-autocomplete>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Distance Rate Applied:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-autocomplete
                  class="v-solo-custom"
                  solo
                  flat
                  multiple
                  :disabled="settingsInputsDisabled"
                  v-model="distanceAppliedController"
                  :items="validServiceTypeList"
                  item-text="optionSelectName"
                  color="orange"
                  label="Service Type"
                  item-value="serviceTypeId"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      :color="viewType === 'EDIT' || viewType === 'NEW' ? 'green' : 'grey darken-3'"
                      close
                      @input="removeServiceTypeIdFromList('DISTANCE', item.serviceTypeId)"
                    >
                      <span>{{ item.optionSelectName }}</span>
                    </v-chip>
                  </template>
                </v-autocomplete>
              </v-flex></v-layout
            >
          </v-flex>
          <!-- <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0"
                    >Point to Point Rate Applied:</h6
                  >
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-autocomplete
                  class="v-solo-custom"
                  solo
                  flat
                  multiple
                  :disabled="settingsInputsDisabled"
                  v-model="p2pAppliedController"
                  :items="validServiceTypeList"
                  item-text="optionSelectName"
                  color="orange"
                  label="Service Type"
                  item-value="serviceTypeId"
                >
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      :color="viewType === 'EDIT' || viewType === 'NEW' ? 'green' : 'grey darken-3'"
                      close
                      @input="removeServiceTypeIdFromList('P2P', item.serviceTypeId)"
                    >
                      <span>{{ item.optionSelectName }}</span>
                    </v-chip>
                  </template>
                </v-autocomplete>
              </v-flex></v-layout
            >
          </v-flex> -->
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pt-4"
                    for="fleetAssetUnitRate"
                  >
                    Unit Rate Applied:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-switch
                  id="fleetAssetUnitRate"
                  color="green"
                  label="Unit Rate Applied"
                  :disabled="!['NEW', 'EDIT'].includes(viewType)"
                  v-model="unitRateAppliesController"
                ></v-switch> </v-flex></v-layout
          ></v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-layout
      row
      wrap
      v-if="serviceRateType === 'FLEET_ASSET' && fleetAssetServiceRate && ['NEW', 'VIEW', 'EDIT'].includes(viewType)"
    >
      <v-flex md12 pt-2>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12>
        <ServiceRateTableCustom
          serviceRateType="FLEET_ASSET"
          ref="fleetAssetServiceRateTableCustom"
          :key="fleetAssetServiceRate.tableId"
          :fleetAssetServiceRate="fleetAssetServiceRate"
          :isNewServiceRates="viewType === 'NEW'"
          @savedRateTable="savedRateTable"
          :fuelSurchargeRate="fuelSurchargeRate"
          :allFleetAssetServiceRatesList="allFleetAssetServiceRates"
          :isEdited="viewType === 'EDIT' || viewType === 'NEW'"
        />
      </v-flex>
    </v-layout>
    <v-layout
      row
      wrap
      v-if="serviceRateType === 'CLIENT' && clientServiceRate && ['NEW', 'VIEW', 'EDIT'].includes(viewType)"
    >
      <v-flex md12 pt-2>
        <v-divider></v-divider>
      </v-flex>
      <v-flex md12>
        <ServiceRateTableCustom
          serviceRateType="CLIENT"
          ref="clientServiceRateTableCustom"
          :key="clientServiceRate.tableId"
          :clientServiceRate="clientServiceRate"
          :isNewServiceRates="viewType === 'NEW'"
          @savedRateTable="savedRateTable"
          :commonAddressList="clientCommonAddresses"
          :fuelSurchargeRate="fuelSurchargeRate"
          :allClientServiceRatesList="allClientServiceRates"
          :isEdited="viewType === 'EDIT' || viewType === 'NEW'"
        />
      </v-flex>
    </v-layout>
  </v-flex>
</section>
