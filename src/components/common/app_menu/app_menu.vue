<template>
  <div class="app-menu">
    <v-menu offset-y :close-on-content-click="false" v-model="menuIsOpen">
      <template v-slot:activator="{ on: menu }">
        <v-btn v-on="menu" flat icon>
          <v-icon size="20">fas fa-bars</v-icon>
        </v-btn>
      </template>
      <v-list class="v-list-custom appbar">
        <v-menu offset-x v-for="(item, index) in menuItems" :key="index">
          <template v-slot:activator="{ on: option }">
            <v-list-tile v-on="option">
              <v-list-tile-title class="title-txt"
                >{{ item.label }}
                <v-icon class="menuIcon" size="12">fa-chevron-right</v-icon>
              </v-list-tile-title>
              <span class="alert-dot" v-if="item.alert"></span>
            </v-list-tile>
          </template>
          <v-list class="v-list-custom appbar">
            <v-list-tile
              v-for="subItem in item.subItems"
              :key="subItem.id"
              @click="callOnClick(subItem)"
            >
              <v-list-tile-title class="title-txt"
                >{{ subItem.label }}
              </v-list-tile-title>
              <span class="alert-dot" v-if="subItem.alert"></span>
            </v-list-tile>
          </v-list>
        </v-menu>
        <v-divider> </v-divider>
        <v-list-tile @click="logout">
          <v-list-tile-title class="logoutIcon"
            ><v-icon size="18">fa-sign-out</v-icon>Logout</v-list-tile-title
          >
        </v-list-tile>
      </v-list>
    </v-menu>
    <div class="alert-badge" v-if="showAlertBadge">
      <i class="fas fa-exclamation"></i>
    </div>
    <SupportTicketDialog
      v-if="supportTicketDialogController"
      :isDialogOpen.sync="supportTicketDialogController"
      :isClientPortal="isClientPortal"
    ></SupportTicketDialog>
    <InvoiceAdjustmentSearch
      v-if="invoiceAdjustmentSearchDialogController"
      :viewingInvoiceAdjustmentSearchDialog="
        invoiceAdjustmentSearchDialogController
      "
    ></InvoiceAdjustmentSearch>
    <InvoiceAdjustmentActions
      v-if="invoiceAdjustmentActionsDialogController"
      :viewingInvoiceAdjustmentActionsDialog="
        invoiceAdjustmentActionsDialogController
      "
    ></InvoiceAdjustmentActions>
    <SupportTicketSearchDialog
      v-if="supportTicketSearchDialogController"
      :viewingSupportTicketSearchDialog="supportTicketSearchDialogController"
      :isClientPortal="isClientPortal"
    ></SupportTicketSearchDialog>

    <GDialog
      v-if="accountRecoveryRequestDialog"
      :width="'90vw'"
      :title="'Account Recovery Requests'"
      :isActionable="false"
      @closeDialog="accountRecoveryRequestDialog = false"
    >
      <div class="pa-3">
        <AccountRecoveryManagement :isOperationsDashboardDialog="true" />
      </div>
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import InvoiceAdjustmentActions from '@/components/support/InvoiceAdjustmentActions/invoice_adjustment_actions.vue';
import InvoiceAdjustmentSearch from '@/components/support/InvoiceAdjustmentSearch/invoice_adjustment_search.vue';
import SupportTicketDialog from '@/components/support/SupportTicket/components/support_ticket_dialog.vue';
import SupportTicketSearchDialog from '@/components/support/SupportTicket/components/support_ticket_search_dialog.vue';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { SupportTicketDialogConfig } from '@/interface-models/SupportTicket/SupportTicketDialog/SupportTicketDialogConfig';
import AccountRecoveryManagement from '@/components/admin/Administration/account-recovery-management/account-recovery-management.vue';
import useAccountRecoveryStore from '@/store/modules/AccountRecoveryStore';
import { useAuthenticationStore } from '@/store/modules/AuthenticationStore';
import { useInvoiceAdjustmentStore } from '@/store/modules/InvoiceAdjustmentStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useSupportTicketStore } from '@/store/modules/SupportTicketStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, ref, Ref } from 'vue';

interface MenuItem {
  id: string;
  label: string;
  subItems: MenuSubItem[];
  visible: boolean;
  active: boolean;
  alert: boolean;
  onClick?: () => void;
}

interface MenuSubItem {
  id: string;
  label: string;
  visible: boolean;
  active: boolean;
  alert: boolean;
  onClick: () => void;
}

const menuIsOpen: Ref<boolean> = ref(false);
const supportTicketDialog: Ref<boolean> = ref(false);
const accountRecoveryRequestDialog: Ref<boolean> = ref(false);

const accountRecoveryStore = useAccountRecoveryStore();
const supportTicketStore = useSupportTicketStore();
const invoiceAdjustmentStore = useInvoiceAdjustmentStore();

// Items to display in menu, including methods to be called on click event
const menuItems = computed((): MenuItem[] => {
  return [
    {
      id: 'Support',
      label: 'Support',
      alert: showAlertBadge.value,
      subItems: [
        {
          id: 'Create Ticket',
          label: 'Create Ticket',
          visible: true,
          active: true,
          alert: false,
          onClick: () => {
            supportTicketDialogController.value = true;
          },
        },
        {
          id: 'Search Support Tickets',
          label: 'Search Support Tickets',
          visible: true,
          active: true,
          alert: false,
          onClick: () => {
            supportTicketSearchDialogController.value = true;
          },
        },
        {
          id: 'Account Recovery Request',
          label: 'Account Recovery Request',
          visible: true,
          active: true,
          alert: showAlertBadge.value,
          onClick: () => {
            accountRecoveryRequestDialog.value = true;
          },
        },
      ],
      visible: true,
      active: true,
    },
    {
      id: 'Invoice Adjustment',
      label: 'Invoice Adjustment',
      alert: false,
      subItems: [
        {
          id: 'View Active Adjustments',
          label: 'View Active Adjustments',
          visible: true,
          active: true,
          alert: false,
          onClick: () => {
            invoiceAdjustmentActionsDialogController.value = true;
          },
        },
        {
          id: 'Search Invoice Adjustments',
          label: 'Search Invoice Adjustments',
          visible: true,
          active: true,
          alert: false,
          onClick: () => {
            invoiceAdjustmentSearchDialogController.value = true;
          },
        },
      ],
      visible: true,
      active: true,
    },
  ];
});

// Modelled to and controls visibility of SupportTicketSearchDialog component
const supportTicketSearchDialogController = computed({
  get: () => supportTicketStore.isViewingSearchDialog,
  set: (value: boolean) => {
    supportTicketStore.setViewingSearchDialog(value);
  },
});

// Modelled to and controls visibility of SupportTicketDialog component
const supportTicketDialogController = computed({
  get: () => supportTicketDialog.value,
  set: (value: boolean) => {
    if (value === false) {
      supportTicketStore.setSupportTicketDialogConfig(null);
    } else {
      const config: SupportTicketDialogConfig = {
        operationType: JobOperationType.NEW,
      };
      supportTicketStore.setSupportTicketDialogConfig(config);
    }
    supportTicketDialog.value = value;
  },
});

// Modelled to and controls visibility of InvoiceAdjustmentSearch component
const invoiceAdjustmentSearchDialogController = computed({
  get: () => invoiceAdjustmentStore.isViewingSearchDialog,
  set: (value: boolean) => {
    invoiceAdjustmentStore.setViewingSearchDialog(value);
  },
});

// Modelled to and controls visibility of InvoiceAdjustmentActions component
const invoiceAdjustmentActionsDialogController = computed({
  get: () => invoiceAdjustmentStore.isViewingActionsDialog,
  set: (value: boolean) => {
    invoiceAdjustmentStore.setViewingActionsDialog(value);
  },
});

const isClientPortal = sessionManager.isClientPortal();

// Call the onClick operation associated with the selected menuItem, then
// close the menu
function callOnClick(menuSubItem: MenuSubItem) {
  menuSubItem.onClick();
  menuIsOpen.value = false;
}

// Log the user out
function logout(): void {
  useOperationsStore().closeAllPopoutWindows();
  useAuthenticationStore().disconnectWebsocket();
}

const showAlertBadge = computed((): boolean => {
  return accountRecoveryStore.accountRecoveryRequestList.length > 0;
});
</script>

<style scoped lang="scss">
.app-menu {
  padding: auto;
  position: relative;
  background-color: var(--background-color-200);

  .v-icon {
    color: var(--light-text-color);
  }
}

.alert-badge {
  position: absolute;
  top: 8px;
  left: 10px;
  font-size: $font-size-11;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(255, 0, 0);
  width: 15px;
  height: 15px;
  border-radius: 50%;
  pointer-events: none;
  i {
    color: var(--text-color);
    font-size: $font-size-10;
  }
}

.alert-dot {
  display: block;
  margin-left: 15px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  pointer-events: none;
  background-color: rgb(255, 0, 0);
}

.menuIcon {
  margin-left: 20px;
  margin-bottom: 3px;
  color: var(--light-text-color);
}

.title-txt {
  color: var(--text-color) !important;
}

.logoutIcon {
  font-weight: 600;
  color: var(--error) !important;
  .v-icon {
    margin-right: 10px;
    color: var(--light-text-color);
  }
}
</style>
